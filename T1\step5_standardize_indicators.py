"""
步骤5：对八项指标进行标准化处理（Min-Max归一化）
注意区分正向指标和负向指标
"""
import pandas as pd
import numpy as np
from sklearn.preprocessing import MinMaxScaler

print("="*80)
print("步骤5：对八项指标进行标准化处理")
print("="*80)

# 读取完整八项指标
all_indicators = pd.read_csv('符合条件企业_完整八项指标.csv')
print(f"待标准化的企业数量: {len(all_indicators)}家")

# 定义八项指标及其方向性
indicators_info = {
    '月利润中位数': {'type': 'positive', 'description': '正向指标 - 越大越好'},
    '企业规模': {'type': 'positive', 'description': '正向指标 - 越大越好'},
    '信用评分': {'type': 'positive', 'description': '正向指标 - 越大越好'},
    '稳定性': {'type': 'positive', 'description': '正向指标 - 越大越好'},
    '税负压力': {'type': 'negative', 'description': '负向指标 - 越小越好'},
    '公司市场竞争力': {'type': 'positive', 'description': '正向指标 - 越大越好'},
    '盈利预测可靠性': {'type': 'positive', 'description': '正向指标 - 越大越好'},
    '经营风险': {'type': 'negative', 'description': '负向指标 - 越小越好'}
}

print("\n指标方向性说明:")
for indicator, info in indicators_info.items():
    print(f"  {indicator}: {info['description']}")

# 提取纯指标数据（排除企业代号等非指标列）
indicator_columns = list(indicators_info.keys())
data_for_standardization = all_indicators[indicator_columns].copy()

print(f"\n标准化前的指标统计:")
for col in indicator_columns:
    data = data_for_standardization[col]
    print(f"{col:12s}: min={data.min():10.4f}, max={data.max():10.4f}, mean={data.mean():8.4f}")

# 创建标准化数据
standardized_data = pd.DataFrame()
standardized_data['企业代号'] = all_indicators['企业代号']

# 对每个指标进行标准化
scaler = MinMaxScaler()

for indicator, info in indicators_info.items():
    original_data = data_for_standardization[indicator].values.reshape(-1, 1)
    
    if info['type'] == 'positive':
        # 正向指标：直接Min-Max标准化
        standardized_values = scaler.fit_transform(original_data).flatten()
    else:
        # 负向指标：先取负数再标准化，或使用 1 - 标准化值
        standardized_values = 1 - scaler.fit_transform(original_data).flatten()
    
    standardized_data[indicator] = standardized_values

print(f"\n标准化后的指标统计:")
for col in indicator_columns:
    data = standardized_data[col]
    print(f"{col:12s}: min={data.min():6.4f}, max={data.max():6.4f}, mean={data.mean():6.4f}")

# 保存标准化后的指标
standardized_data.to_csv('符合条件企业_标准化八项指标.csv', index=False, encoding='utf-8-sig')
print(f"\n✅ 标准化八项指标已保存至: 符合条件企业_标准化八项指标.csv")

# 显示前10家企业标准化后的指标
print(f"\n前10家企业标准化后的八项指标:")
pd.set_option('display.precision', 4)
print(standardized_data.head(10))

# 数据验证
print(f"\n✅ 标准化验证:")
print(f"- 所有指标值都在[0,1]区间: {((standardized_data[indicator_columns] >= 0) & (standardized_data[indicator_columns] <= 1)).all().all()}")
print(f"- 无缺失值: {standardized_data.isnull().sum().sum() == 0}")
print(f"- 企业数量保持99家: {len(standardized_data) == 99}")

print(f"\n下一步：基于标准化指标计算AHP综合得分")
