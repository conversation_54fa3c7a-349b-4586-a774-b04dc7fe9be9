"""
基于PCA排名的信贷策略分析
分析PCA分数为0的原因并制定信贷分配方案
"""
import pandas as pd
import numpy as np
from sklearn.preprocessing import MinMaxScaler
from sklearn.decomposition import PCA
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

print("="*70)
print("基于PCA排名的信贷策略分析")
print("="*70)

# 读取数据
data = pd.read_csv('企业四项指标原始数据.csv')
indicators = ['最终信用分数', '盈利能力', '企业规模', '稳定性']
raw_data = data[indicators].values
companies = data['企业代号'].values

# 标准化
scaler = MinMaxScaler()
standardized_data = scaler.fit_transform(raw_data)

# PCA分析
pca = PCA()
pca_result = pca.fit_transform(standardized_data)

print("1. PCA分数为0的原因分析")
print("="*50)

# 第一主成分得分
pca_scores_raw = pca_result[:, 0] * pca.explained_variance_ratio_[0]
print(f"原始PCA得分范围: [{pca_scores_raw.min():.6f}, {pca_scores_raw.max():.6f}]")

# 标准化到0-1（这就是为什么有些分数为0的原因）
pca_scores = (pca_scores_raw - pca_scores_raw.min()) / (pca_scores_raw.max() - pca_scores_raw.min())
print(f"标准化后PCA得分范围: [{pca_scores.min():.6f}, {pca_scores.max():.6f}]")

# 找出PCA分数为0的企业
zero_score_mask = pca_scores < 0.0001  # 近似为0
zero_score_companies = companies[zero_score_mask]
print(f"\nPCA分数为0（或接近0）的企业数量: {zero_score_mask.sum()}")
print(f"这些企业是: {list(zero_score_companies)}")

print(f"\n原因分析:")
print(f"- PCA得分标准化到0-1区间时，最低得分企业被映射为0")
print(f"- 这些企业在第一主成分上的表现最差")
print(f"- 主要是信用分数为0分的高风险企业")

# 分析这些企业的特征
print(f"\nPCA分数为0的企业特征分析:")
zero_companies_data = data[data['企业代号'].isin(zero_score_companies)][['企业代号'] + indicators]
print(zero_companies_data.head(10).round(2).to_string(index=False))

print(f"\n2. 第一主成分载荷分析")
print("="*50)
loadings = pca.components_[0]
print("第一主成分各指标载荷:")
for i, indicator in enumerate(indicators):
    print(f"  {indicator}: {loadings[i]:.4f}")

print(f"\n第一主成分解释:")
max_loading_idx = np.argmax(np.abs(loadings))
print(f"主要影响因子: {indicators[max_loading_idx]} (载荷: {loadings[max_loading_idx]:.4f})")

print(f"\n3. 信贷策略制定")
print("="*50)

# 读取完整的PCA结果
results = pd.read_csv('标准化后综合评估结果.csv')

# 基于PCA得分制定信贷策略
print(f"基于PCA得分的信贷分配策略:")
print(f"- 年度信贷总额固定，根据PCA得分分配")
print(f"- PCA得分反映企业综合风险和价值")
print(f"- 得分越高，分配比例越大")

# 信贷分配方案设计
def calculate_credit_allocation(pca_scores, companies, min_threshold=0.001):
    """
    计算信贷分配比例
    pca_scores: PCA得分
    companies: 企业代号
    min_threshold: 最低分配门槛
    """
    # 对于分数为0或接近0的企业，设置最低门槛
    adjusted_scores = np.maximum(pca_scores, min_threshold)
    
    # 计算基础分配比例（基于得分）
    base_allocation = adjusted_scores / adjusted_scores.sum()
    
    # 风险调整：PCA得分为0的企业大幅降低分配
    risk_adjustment = np.where(pca_scores < 0.001, 0.1, 1.0)  # 高风险企业只分配10%
    
    adjusted_allocation = base_allocation * risk_adjustment
    final_allocation = adjusted_allocation / adjusted_allocation.sum()
    
    return final_allocation, base_allocation, risk_adjustment

# 计算分配比例
final_allocation, base_allocation, risk_adjustment = calculate_credit_allocation(
    results['PCA得分'].values, results['企业代号'].values
)

# 创建信贷分配结果
credit_strategy = pd.DataFrame({
    '企业代号': results['企业代号'],
    'PCA得分': results['PCA得分'],
    'PCA排名': results['PCA排名'],
    '信用分数': results['最终信用分数_标准化'],
    '基础分配比例(%)': base_allocation * 100,
    '风险调整系数': risk_adjustment,
    '最终分配比例(%)': final_allocation * 100,
    '风险等级': np.where(results['PCA得分'] < 0.001, '高风险', 
                      np.where(results['PCA得分'] < 0.3, '中等风险', '低风险'))
})

# 按分配比例排序
credit_strategy_sorted = credit_strategy.sort_values('最终分配比例(%)', ascending=False)

print(f"\n前30名企业信贷分配方案:")
print("=" * 120)
top30_columns = ['企业代号', 'PCA得分', 'PCA排名', '信用分数', '最终分配比例(%)', '风险等级']
print(credit_strategy_sorted[top30_columns].head(30).round(4).to_string(index=False))

print(f"\n高风险企业分配方案（PCA得分接近0）:")
print("=" * 100)
high_risk = credit_strategy_sorted[credit_strategy_sorted['风险等级'] == '高风险'].head(10)
print(high_risk[['企业代号', 'PCA得分', '信用分数', '最终分配比例(%)', '风险等级']].round(4).to_string(index=False))

print(f"\n4. 信贷策略汇总")
print("="*50)

# 按风险等级汇总
risk_summary = credit_strategy.groupby('风险等级').agg({
    '企业代号': 'count',
    '最终分配比例(%)': ['sum', 'mean', 'std']
}).round(2)

print("按风险等级汇总:")
print(risk_summary)

# 具体分配建议
print(f"\n5. 具体分配建议")
print("="*50)

total_budget = 1000000000  # 假设年度信贷总额10亿元
print(f"假设年度信贷总额: {total_budget:,} 元")

print(f"\n分配策略:")
print(f"🏆 顶级企业（前10名）:")
top10_allocation = credit_strategy_sorted.head(10)['最终分配比例(%)'].sum()
print(f"   分配比例: {top10_allocation:.2f}%")
print(f"   分配金额: {total_budget * top10_allocation / 100:,.0f} 元")

print(f"\n🔸 优质企业（11-30名）:")
good_allocation = credit_strategy_sorted.iloc[10:30]['最终分配比例(%)'].sum()
print(f"   分配比例: {good_allocation:.2f}%")
print(f"   分配金额: {total_budget * good_allocation / 100:,.0f} 元")

print(f"\n⚠️  中等风险企业:")
medium_risk_count = (credit_strategy['风险等级'] == '中等风险').sum()
medium_allocation = credit_strategy[credit_strategy['风险等级'] == '中等风险']['最终分配比例(%)'].sum()
print(f"   企业数量: {medium_risk_count}")
print(f"   分配比例: {medium_allocation:.2f}%")
print(f"   分配金额: {total_budget * medium_allocation / 100:,.0f} 元")

print(f"\n🚫 高风险企业（PCA得分≈0）:")
high_risk_count = (credit_strategy['风险等级'] == '高风险').sum()
high_allocation = credit_strategy[credit_strategy['风险等级'] == '高风险']['最终分配比例(%)'].sum()
print(f"   企业数量: {high_risk_count}")
print(f"   分配比例: {high_allocation:.2f}%")
print(f"   分配金额: {total_budget * high_allocation / 100:,.0f} 元")

print(f"\n6. 风险管理建议")
print("="*50)
print(f"""
💡 信贷策略要点:

1. 优先配置策略:
   - 前10名企业: 获得约{top10_allocation:.1f}%的资金，单体平均{top10_allocation/10:.2f}%
   - 这些企业PCA得分高，综合风险低，回报稳定

2. 高风险企业处理:
   - {high_risk_count}家PCA得分接近0的企业主要是信用分数0分企业
   - 建议暂缓放贷或要求额外担保
   - 如必须放贷，额度控制在平均水平的10%以内

3. 动态调整机制:
   - 定期重新评估企业PCA得分
   - 根据实际还款情况调整分配比例
   - 建立预警机制，及时识别风险变化

4. PCA得分为0的原因:
   - 技术原因: Min-Max标准化将最低得分映射为0
   - 业务原因: 这些企业在所有维度上表现都较差
   - 建议: 可考虑使用原始PCA得分进行更细致的区分
""")

# 保存信贷策略结果
credit_strategy_sorted.round(4).to_csv('基于PCA的信贷分配策略.csv', index=False, encoding='utf-8-sig')
print(f"\n✅ 信贷分配策略已保存至: 基于PCA的信贷分配策略.csv")

print(f"\n7. 验证检查")
print("="*50)
print(f"分配比例总和: {final_allocation.sum():.6f} (应该等于1.0)")
print(f"最大单体分配比例: {final_allocation.max()*100:.4f}%")
print(f"最小单体分配比例: {final_allocation.min()*100:.6f}%")
print(f"平均分配比例: {final_allocation.mean()*100:.4f}%")
