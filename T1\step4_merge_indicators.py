"""
步骤4：合并基础指标和税务指标，形成完整的八项指标体系
"""
import pandas as pd
import numpy as np

print("="*80)
print("步骤4：合并基础指标和税务指标，形成完整八项指标")
print("="*80)

# 读取基础四项指标
basic_indicators = pd.read_csv('符合条件企业_基础四项指标.csv')
print(f"基础四项指标: {len(basic_indicators)}家企业")

# 读取税务四项指标
tax_indicators = pd.read_csv('符合条件企业_税务四项指标.csv')
print(f"税务四项指标: {len(tax_indicators)}家企业")

# 合并指标
all_indicators = pd.merge(basic_indicators, tax_indicators, on='企业代号', how='inner')
print(f"合并后企业数量: {len(all_indicators)}家")

# 检查合并结果
print("\n✅ 完整八项指标体系:")
print("基础四项：月利润中位数、企业规模、信用评分、稳定性")
print("税务四项：税负压力、公司市场竞争力、盈利预测可靠性、经营风险")

# 显示指标统计信息
print("\n各指标统计摘要:")
indicators = ['月利润中位数', '企业规模', '信用评分', '稳定性', 
             '税负压力', '公司市场竞争力', '盈利预测可靠性', '经营风险']

for indicator in indicators:
    data = all_indicators[indicator]
    print(f"{indicator:12s}: min={data.min():10.4f}, max={data.max():10.4f}, mean={data.mean():8.4f}")

# 保存完整八项指标
all_indicators.to_csv('符合条件企业_完整八项指标.csv', index=False, encoding='utf-8-sig')
print(f"\n✅ 完整八项指标已保存至: 符合条件企业_完整八项指标.csv")

# 显示前10家企业的完整指标
print(f"\n前10家企业完整八项指标预览:")
pd.set_option('display.width', None)
pd.set_option('display.max_columns', None)
pd.set_option('display.precision', 4)
print(all_indicators.head(10))

print(f"\n数据验证:")
print(f"- 企业数量: {len(all_indicators)}家 (应为99家)")
print(f"- 指标数量: {len(all_indicators.columns)-1}项 (应为8项)")
print(f"- 是否有缺失值: {all_indicators.isnull().sum().sum()}个")

print(f"\n下一步：对八项指标进行标准化处理")
