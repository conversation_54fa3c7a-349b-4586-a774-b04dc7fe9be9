"""
T2企业信贷分配优化结果可视化
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.font_manager import FontProperties

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def visualize_optimization_results():
    """可视化优化结果"""
    
    try:
        # 读取结果文件
        pareto_summary = pd.read_csv('T2_Pareto最优解汇总.csv')
        allocation_detail = pd.read_csv('T2_推荐信贷分配方案.csv')
        
        # 创建子图
        fig = plt.figure(figsize=(20, 15))
        
        # 1. Pareto前沿图
        ax1 = plt.subplot(3, 3, 1)
        plt.scatter(pareto_summary['总风险(元)'], pareto_summary['总收益(元)'], 
                   c='red', s=100, alpha=0.7, edgecolors='black')
        plt.xlabel('总风险 (元)')
        plt.ylabel('总收益 (元)')
        plt.title('Pareto前沿 - 收益与风险权衡')
        plt.grid(True, alpha=0.3)
        
        # 添加数据点标签
        for idx, row in pareto_summary.iterrows():
            plt.annotate(f"解{row['解ID']}", 
                        (row['总风险(元)'], row['总收益(元)']),
                        xytext=(5, 5), textcoords='offset points',
                        fontsize=8, alpha=0.8)
        
        # 2. 利率分布
        ax2 = plt.subplot(3, 3, 2)
        plt.hist(allocation_detail['贷款利率(%)'], bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        plt.xlabel('贷款利率 (%)')
        plt.ylabel('企业数量')
        plt.title('推荐方案利率分布')
        plt.grid(True, alpha=0.3)
        
        # 3. 各评级资金分配
        ax3 = plt.subplot(3, 3, 3)
        rating_allocation = allocation_detail.groupby('信誉评级')['贷款金额(元)'].sum()
        colors = ['#ff9999', '#66b3ff', '#99ff99']
        plt.pie(rating_allocation.values, labels=rating_allocation.index, autopct='%1.1f%%',
               colors=colors, startangle=90)
        plt.title('各信誉评级资金分配比例')
        
        # 4. 收益风险散点图
        ax4 = plt.subplot(3, 3, 4)
        plt.scatter(pareto_summary['总风险(元)'], pareto_summary['总收益(元)'], 
                   s=pareto_summary['总放贷额(元)']/1000000, alpha=0.6, c='green')
        plt.xlabel('总风险 (元)')
        plt.ylabel('总收益 (元)')
        plt.title('收益-风险-放贷额关系\n(气泡大小=放贷额)')
        plt.grid(True, alpha=0.3)
        
        # 5. 各评级平均利率
        ax5 = plt.subplot(3, 3, 5)
        rating_rates = allocation_detail.groupby('信誉评级')['贷款利率(%)'].agg(['mean', 'std', 'count'])
        rating_rates['mean'].plot(kind='bar', color=['red', 'orange', 'yellow'], alpha=0.7)
        plt.ylabel('平均利率 (%)')
        plt.title('各信誉评级平均利率')
        plt.xticks(rotation=0)
        plt.grid(True, alpha=0.3)
        
        # 6. 贷款金额分布
        ax6 = plt.subplot(3, 3, 6)
        allocation_detail.boxplot(column='贷款金额(元)', by='信誉评级', ax=ax6)
        plt.ylabel('贷款金额 (元)')
        plt.title('各信誉评级贷款金额分布')
        plt.xticks(rotation=0)
        
        # 7. 流失率分析
        ax7 = plt.subplot(3, 3, 7)
        allocation_detail.boxplot(column='预期流失率(%)', by='信誉评级', ax=ax7)
        plt.ylabel('预期流失率 (%)')
        plt.title('各信誉评级预期流失率分布')
        plt.xticks(rotation=0)
        
        # 8. 收益风险比分析
        ax8 = plt.subplot(3, 3, 8)
        pareto_summary['收益风险比'] = pareto_summary['总收益(元)'] / (pareto_summary['总风险(元)'] + 1)
        plt.bar(range(len(pareto_summary)), pareto_summary['收益风险比'], 
               alpha=0.7, color='purple')
        plt.xlabel('解编号')
        plt.ylabel('收益风险比')
        plt.title('各解收益风险比对比')
        plt.xticks(range(len(pareto_summary)), [f"解{i+1}" for i in range(len(pareto_summary))], rotation=45)
        plt.grid(True, alpha=0.3)
        
        # 9. 企业数量分布
        ax9 = plt.subplot(3, 3, 9)
        rating_counts = allocation_detail['信誉评级'].value_counts().sort_index()
        rating_counts.plot(kind='bar', color=['red', 'orange', 'yellow'], alpha=0.7)
        plt.ylabel('企业数量')
        plt.title('各信誉评级放贷企业数量')
        plt.xticks(rotation=0)
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('T2_优化结果可视化.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # 打印统计摘要
        print("="*60)
        print("📊 T2企业信贷分配优化结果统计摘要")
        print("="*60)
        
        print(f"\n🎯 Pareto前沿解概况:")
        print(f"   解的数量: {len(pareto_summary)}个")
        print(f"   收益范围: {pareto_summary['总收益(元)'].min():,.0f} - {pareto_summary['总收益(元)'].max():,.0f}元")
        print(f"   风险范围: {pareto_summary['总风险(元)'].min():,.0f} - {pareto_summary['总风险(元)'].max():,.0f}元")
        print(f"   放贷额范围: {pareto_summary['总放贷额(元)'].min():,.0f} - {pareto_summary['总放贷额(元)'].max():,.0f}元")
        
        print(f"\n💰 推荐方案详情:")
        total_loan = allocation_detail['贷款金额(元)'].sum()
        avg_rate = np.average(allocation_detail['贷款利率(%)'], weights=allocation_detail['贷款金额(元)'])
        total_return = allocation_detail['预期收益(元)'].sum()
        total_risk = allocation_detail['风险损失(元)'].sum()
        
        print(f"   放贷企业数: {len(allocation_detail)}家")
        print(f"   总放贷金额: {total_loan:,.0f}元 ({total_loan/100000000:.1%}预算利用率)")
        print(f"   加权平均利率: {avg_rate:.2f}%")
        print(f"   预期总收益: {total_return:,.0f}元")
        print(f"   预期总风险: {total_risk:,.0f}元")
        print(f"   收益风险比: {total_return/(total_risk+1):.2f}")
        
        print(f"\n🏢 各信誉评级分配:")
        for rating in ['A', 'B', 'C']:
            rating_data = allocation_detail[allocation_detail['信誉评级'] == rating]
            if len(rating_data) > 0:
                rating_loan = rating_data['贷款金额(元)'].sum()
                rating_return = rating_data['预期收益(元)'].sum()
                rating_risk = rating_data['风险损失(元)'].sum()
                avg_rate = rating_data['贷款利率(%)'].mean()
                
                print(f"   {rating}级 ({len(rating_data)}家): ")
                print(f"      分配金额: {rating_loan:,.0f}元 ({rating_loan/total_loan:.1%})")
                print(f"      平均利率: {avg_rate:.2f}%")
                print(f"      预期收益: {rating_return:,.0f}元")
                print(f"      预期风险: {rating_risk:,.0f}元")
        
        return pareto_summary, allocation_detail
        
    except FileNotFoundError as e:
        print(f"❌ 错误: 找不到结果文件 {e}")
        print("请先运行 nsga2_credit_optimization.py 生成结果文件")
        return None, None

def analyze_strategy_comparison():
    """分析不同策略的对比"""
    try:
        pareto_summary = pd.read_csv('T2_Pareto最优解汇总.csv')
        
        # 计算收益风险比
        pareto_summary['收益风险比'] = pareto_summary['总收益(元)'] / (pareto_summary['总风险(元)'] + 1)
        
        # 识别三种策略
        conservative_idx = pareto_summary['总风险(元)'].idxmin()
        aggressive_idx = pareto_summary['总收益(元)'].idxmax() 
        balanced_idx = pareto_summary['收益风险比'].idxmax()
        
        strategies = {
            '保守策略': pareto_summary.iloc[conservative_idx],
            '均衡策略': pareto_summary.iloc[balanced_idx],
            '激进策略': pareto_summary.iloc[aggressive_idx]
        }
        
        print("\n📈 投资策略对比分析:")
        print("="*80)
        
        strategy_comparison = []
        for name, strategy in strategies.items():
            strategy_comparison.append({
                '策略类型': name,
                '总收益(元)': strategy['总收益(元)'],
                '总风险(元)': strategy['总风险(元)'],
                '收益风险比': strategy['收益风险比'],
                '总放贷额(元)': strategy['总放贷额(元)'],
                '平均利率(%)': strategy['平均利率(%)'],
                '放贷企业数': strategy['放贷企业数']
            })
            
            print(f"\n🎯 {name}:")
            print(f"   预期收益: {strategy['总收益(元)']:,.0f}元")
            print(f"   预期风险: {strategy['总风险(元)']:,.0f}元") 
            print(f"   收益风险比: {strategy['收益风险比']:.2f}")
            print(f"   资金利用: {strategy['总放贷额(元)']:,.0f}元 ({strategy['总放贷额(元)']/100000000:.1%})")
            print(f"   平均利率: {strategy['平均利率(%)']:.2f}%")
            print(f"   放贷企业: {strategy['放贷企业数']}家")
        
        # 保存对比结果
        comparison_df = pd.DataFrame(strategy_comparison)
        comparison_df.to_csv('T2_策略对比分析.csv', index=False, encoding='utf-8-sig')
        
        return comparison_df
        
    except FileNotFoundError:
        print("❌ 错误: 请先运行优化算法生成结果文件")
        return None

if __name__ == "__main__":
    print("🎨 开始可视化T2企业信贷分配优化结果...")
    
    # 可视化结果
    pareto_data, allocation_data = visualize_optimization_results()
    
    if pareto_data is not None:
        # 策略对比分析
        comparison_data = analyze_strategy_comparison()
        
        print("\n✅ 可视化完成!")
        print("   - 图表已保存为: T2_优化结果可视化.png")
        print("   - 策略对比已保存为: T2_策略对比分析.csv")
    
    input("\n按Enter键退出...")
