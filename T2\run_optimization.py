"""
运行T2企业信贷分配优化的主脚本
包含数据检查、算法运行、结果分析的完整流程
"""

import os
import pandas as pd
import numpy as np

def check_data_files():
    """检查数据文件是否存在"""
    required_files = [
        'T2企业_优化算法输入.csv',
        '4.csv'
    ]
    
    print("🔍 检查数据文件...")
    missing_files = []
    
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
        else:
            df = pd.read_csv(file)
            print(f"   ✅ {file}: {len(df)}行数据")
    
    if missing_files:
        print(f"❌ 缺少文件: {missing_files}")
        return False
    
    return True

def quick_data_preview():
    """快速预览数据"""
    print("\n📊 数据预览:")
    
    # 企业数据预览
    enterprises = pd.read_csv('T2企业_优化算法输入.csv')
    print(f"   企业总数: {len(enterprises)}家")
    
    rating_dist = enterprises['继承信誉评级'].value_counts()
    print(f"   评级分布: A级{rating_dist.get('A', 0)}家, B级{rating_dist.get('B', 0)}家, C级{rating_dist.get('C', 0)}家")
    
    # 流失率数据预览
    churn_data = pd.read_csv('4.csv')
    print(f"   流失率数据: {len(churn_data)}行")
    
    # 显示前5家企业
    print("\n   前5家企业样本:")
    print(enterprises[['企业代号', '继承信誉评级', '相似度得分']].head())
    
    return enterprises, churn_data

def run_optimization_test():
    """运行小规模测试"""
    print("\n🧪 运行小规模测试...")
    
    try:
        # 导入优化器（使用较小参数进行快速测试）
        from nsga2_credit_optimization import CreditAllocationOptimizer
        
        # 创建测试优化器
        optimizer = CreditAllocationOptimizer()
        
        # 修改为小规模测试参数
        optimizer.POPULATION_SIZE = 20
        optimizer.GENERATIONS = 50
        
        print("⚡ 开始小规模测试（20个个体，50代进化）...")
        
        # 运行优化
        population, pareto_front, evolution_log = optimizer.run_optimization()
        
        # 简单分析
        solutions, conservative, balanced, aggressive = optimizer.analyze_solutions(pareto_front)
        
        print(f"\n✅ 测试完成！")
        print(f"   找到 {len(pareto_front)} 个Pareto最优解")
        print(f"   推荐解：收益{balanced['total_return']:,.0f}元，风险{balanced['total_risk']:,.0f}元")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def run_full_optimization():
    """运行完整优化"""
    print("\n🚀 运行完整优化...")
    
    try:
        from nsga2_credit_optimization import main
        
        # 运行主函数
        optimizer, solutions, allocation_df = main()
        
        print(f"\n🎉 完整优化成功完成!")
        
        return True
        
    except Exception as e:
        print(f"❌ 完整优化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_visualization():
    """运行结果可视化"""
    print("\n🎨 运行结果可视化...")
    
    try:
        from visualize_results import visualize_optimization_results, analyze_strategy_comparison
        
        # 可视化结果
        pareto_data, allocation_data = visualize_optimization_results()
        
        if pareto_data is not None:
            # 策略对比
            comparison_data = analyze_strategy_comparison()
            print("✅ 可视化完成!")
            return True
        else:
            print("❌ 可视化失败: 缺少结果文件")
            return False
            
    except Exception as e:
        print(f"❌ 可视化失败: {e}")
        return False

def main():
    """主运行流程"""
    print("="*80)
    print("🏦 T2企业信贷分配优化系统")
    print("="*80)
    
    # 1. 数据检查
    if not check_data_files():
        print("❌ 数据文件检查失败，请确保相关文件存在")
        return
    
    # 2. 数据预览
    enterprises, churn_data = quick_data_preview()
    
    # 3. 用户选择运行模式
    print("\n🎛️ 请选择运行模式:")
    print("   1. 快速测试（小规模，约1分钟）")
    print("   2. 完整优化（大规模，约10-15分钟）")
    print("   3. 仅可视化（需要已有结果文件）")
    print("   4. 全流程（测试+完整优化+可视化）")
    
    while True:
        try:
            choice = input("\n请输入选择 (1-4): ").strip()
            
            if choice == '1':
                print("\n开始快速测试...")
                if run_optimization_test():
                    print("✅ 快速测试成功完成!")
                break
                
            elif choice == '2':
                print("\n开始完整优化...")
                if run_full_optimization():
                    print("✅ 完整优化成功完成!")
                    
                    # 询问是否进行可视化
                    viz_choice = input("\n是否运行结果可视化? (y/n): ").strip().lower()
                    if viz_choice in ['y', 'yes']:
                        run_visualization()
                break
                
            elif choice == '3':
                print("\n开始结果可视化...")
                run_visualization()
                break
                
            elif choice == '4':
                print("\n开始全流程处理...")
                
                # 快速测试
                if run_optimization_test():
                    print("\n✅ 快速测试通过，继续完整优化...")
                    
                    # 完整优化
                    if run_full_optimization():
                        print("\n✅ 完整优化成功，开始可视化...")
                        
                        # 可视化
                        run_visualization()
                        
                        print("\n🎉 全流程成功完成!")
                    else:
                        print("❌ 完整优化失败")
                else:
                    print("❌ 快速测试失败，跳过后续步骤")
                break
                
            else:
                print("❌ 无效选择，请输入1-4之间的数字")
                continue
                
        except KeyboardInterrupt:
            print("\n\n👋 用户取消操作")
            break
        except Exception as e:
            print(f"❌ 输入错误: {e}")
            continue
    
    print(f"\n📝 运行完成!")
    print(f"   生成的文件可能包括:")
    print(f"   - T2_Pareto最优解汇总.csv")
    print(f"   - T2_推荐信贷分配方案.csv")
    print(f"   - T2_策略对比分析.csv")
    print(f"   - T2_优化结果可视化.png")

if __name__ == "__main__":
    main()
