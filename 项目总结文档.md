# 2020年数学建模竞赛C题：中小微企业信贷决策项目总结

## 项目背景
- **竞赛题目**: 2020年数学建模竞赛C题 - 中小微企业信贷决策
- **主要目标**: 为T1（123家）和T2（302家）中小微企业制定最优信贷投放策略
- **预算约束**: T2题目要求1亿元总预算，利率范围4%-15%
- **工作目录**: `f:\2024数模培训\2020c`

## 项目结构
```
2020c/
├── 题目.txt                    # 比赛题目原文
├── 备用/                       # 原始数据文件
│   ├── 1.xlsx, 2.xlsx, 3.xlsx # T1相关数据
│   └── T2.1.xls, T2.2.xlsx... # T2相关数据
├── T1/                         # 第一题完成的文件
│   ├── 正确的完整八项指标.csv   # T1企业8项财务指标
│   ├── 标准化八项指标.csv       # T1企业标准化后指标
│   ├── 符合条件企业名单.csv     # T1企业信誉评级（99家，排除D级）
│   ├── 最终正确的信贷投放策略.csv # AHP方法结果
│   ├── PCA方法信贷投放策略.csv  # PCA方法结果
│   └── AHP与PCA方法对比结果.csv # 两种方法对比
└── T2/                         # 第二题已接近完成
    ├── 4.csv                   # 客户流失率数据
    ├── T2.1.csv, T2.2.csv...  # T2企业原始数据
    ├── step1_calculate_t2_indicators.py    # 计算T2企业7项指标 ✅
    ├── step2_final_similarity_matching.py  # ABCD完整匹配+D级排除 ✅
    ├── generate_final_t2_data.py           # 生成最终投放数据集 ✅
    ├── T2企业_七项指标.csv      # T2企业7项财务指标
    ├── T2企业_七项指标_标准化.csv # 标准化后的7项指标
    ├── T2企业_最终投放数据集.csv # 299家可投放企业完整数据
    ├── T2企业_优化算法输入.csv   # 优化算法专用数据集
    └── normalize_t2_indicators.py # 指标标准化脚本 ✅
```

## 已完成工作

### 第一题 (T1) - 完成 ✅
**问题**: 为123家企业制定信贷策略，筛选合格企业并确定贷款利率

**关键发现**: 
- 原始123家企业中，24家为D级信誉评级需排除（题目明确"D级不予贷款"）
- 最终99家合格企业：A级27家，B级38家，C级34家

**技术方案**:
1. **数据预处理**: 计算8项财务指标（4项基本+4项税务指标）
2. **AHP层次分析法**: 主观赋权，E42企业表现最优（AHP得分0.6945，利率4.00%）
3. **PCA主成分分析法**: 客观降维，提供备选方案
4. **最终策略**: 4.00%-15.00%利率范围，平均利率8.42%

**核心指标**:
- **负债水平** = 最终信用分数（不是计算的负债比例）
- **盈利能力** = 年度利润总额
- **现金流稳定性** = 现金流变异系数的倒数
- **企业规模** = 年度营业收入总额
- **税负压力** = (应纳税额-实际纳税额)/营业收入
- **公司市场竞争力** = 营业收入/资产总额
- **盈利预测可靠性** = 基于历史数据的预测准确度
- **经营风险** = 综合风险评估指标

### 第二题 (T2) - 进行中 🚧
**问题**: 为302家企业制定1亿元信贷分配策略，利率4%-15%，目标收益最大化+流失率最小化

**当前进展**:

#### 步骤1: T2企业财务指标计算 ✅
- **文件**: `step1_calculate_t2_indicators.py`
- **输出**: `T2企业_七项指标.csv`（302家企业，7项指标）
- **数据来源**: T2.1.csv (进项发票), T2.2.csv (销项发票), T2.3.csv (企业信息)
- **指标统计**:
  - 盈利能力: [-2,676,988, 13,148,611]，均值463,380
  - 现金流稳定性: [0.001, 0.941]，均值0.360
  - 企业规模: [2,058, 66,105,622]，均值2,536,516
  - 其他指标已计算完成

#### 步骤2: 相似度匹配 ✅
- **策略升级**: ABCD完整匹配 + D级自动排除
- **T1数据集**: 生成包含123家企业（A27家、B37家、C32家、D27家）的完整标准化数据
- **匹配方法**: 基于T1完整数据，使用欧氏距离进行相似度匹配
- **自动筛选**: T2企业与T1所有等级匹配，继承到D级的企业自动排除
- **最终筛选结果**:
  - **原始T2企业**: 302家
  - **排除D级企业**: 3家（E397, E400, E419）
  - **可投放信贷企业**: 299家
    - **A级**: 44家 (14.7%)
    - **B级**: 215家 (71.9%)
    - **C级**: 40家 (13.4%)
- **质量评估**: 平均相似度0.6056，99%企业通过风险筛选
- **输出文件**:
  - `T2企业_最终投放数据集.csv`（完整投放企业数据）
  - `T2企业_优化算法输入.csv`（优化算法专用数据）

#### 步骤3: 多目标优化 (待定) 🎯
- **优化目标**: 期望收益最大化 + 风险最小化（权重相同）
- **投放对象**: 299家A/B/C级企业
- **算法选择**: 启发式算法（类型待定）
- **约束条件**: 
  - 总预算≤1亿元
  - 利率∈[4%, 15%]
  - 风险分散配置

## 技术栈
- **Python**: pandas, numpy, sklearn
- **机器学习**: StandardScaler, MinMaxScaler, PCA
- **相似度计算**: euclidean_distances, cosine_similarity
- **优化算法**: 遗传算法, 模拟退火
- **数据可视化**: matplotlib (待使用)

## 数据流转关系
```
T1原始数据 → 计算8项指标 → AHP/PCA分析 → 信贷策略
    ↓
T1标准化指标 → 与T2进行相似度匹配 → T2继承信誉评级
    ↓
T2(含评级) + 客户流失率模型 → 多目标优化 → 最终分配策略
```

## 关键文件说明

### 输入数据
1. **T1企业指标**: `T1/正确的完整八项指标.csv` (99×8)
2. **T1企业评级**: `T1/符合条件企业名单.csv` (99×4)
3. **T1标准化数据**: `T1/标准化八项指标.csv` (99×8，0-1标准化)
4. **T2企业数据**: `T2/T2.1.csv`, `T2/T2.2.csv`, `T2/T2.3.csv`
5. **流失率数据**: `T2/4.csv` (利率vs各评级流失率对应表)

### 中间数据
1. **T2财务指标**: `T2/T2企业_七项指标.csv` (302×8)
2. **相似度匹配**: `T2/T2企业_最终相似度匹配结果.csv` (302×6)
3. **T2完整数据**: `T2/T2企业_最终完整数据.csv` (302×11)

### 输出结果
1. **T1最终策略**: `T1/最终正确的信贷投放策略.csv`
2. **T2最终策略**: `T2/T2遗传算法_最优信贷分配策略.csv` (待生成)

## 核心算法详解

### 1. 相似度匹配算法
```python
# 使用欧氏距离计算T2与T1企业相似度
distances = euclidean_distances(t2_normalized, t1_standardized)
similarity_scores = 1 - (distances / max_distance)
# 选择最相似的T1企业，继承其信誉评级
```

### 2. 客户流失率预测
```python
# 基于4.csv构建分段线性插值函数
churn_rate = interpolator[rating](interest_rate)
```

### 3. 多目标优化
```python
# 目标函数
fitness = 0.7 * (expected_return/budget) - 0.3 * (churn_risk/loan_amount)
# 约束条件
total_loan_amount <= 100_000_000
interest_rate in [0.04, 0.15]
```

## 当前状态与下一步

### ✅ 已完成
1. T1企业完整分析和策略制定
2. T2企业7项财务指标计算
3. T1-T2相似度匹配和信誉评级继承
4. 客户流失率预测模型构建
5. 遗传算法框架设计


### 📋 待完成
1. 运行完整的优化算法
2. 结果可视化和分析报告
3. 策略有效性验证
4. 模型鲁棒性测试

## 关键参数配置
- **T1合格企业**: 99家 (排除24家D级)
- **T2评级分布**: A级46家，B级180家，C级76家
- **预算约束**: 1亿元
- **利率区间**: [4%, 15%]
- **优化目标**: 收益最大化 + 风险最小化
- **算法参数**: 种群50，进化100代，变异率15%

## 项目特色
1. **真实数据驱动**: 使用真实T1企业数据进行相似度匹配
2. **多算法集成**: AHP+PCA+遗传算法+模拟退火
3. **风险控制**: 客户流失率预测和风险平衡
4. **约束优化**: 严格的预算和利率约束
5. **可解释性**: 详细的匹配过程和决策逻辑

## 注意事项
1. **D级企业排除**: T1原始123家中24家D级必须排除
2. **标准化一致性**: T1和T2必须使用相同的标准化方法
3. **评级继承**: T2通过相似度匹配继承T1企业信誉评级
4. **流失率模型**: 基于4.csv的分评级流失率数据
5. **多目标平衡**: 收益与风险的权衡需要调参优化

---

**项目状态**: T1完成，T2算法设计完成，待运行最终优化
**最后更新**: 2025年8月7日
**技术负责**: GitHub Copilot
**数据完整性**: ✅ 验证通过
