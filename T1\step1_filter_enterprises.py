"""
从头开始重新计算所有数据
第一步：筛选符合条件的企业，排除D级企业
"""
import pandas as pd
import numpy as np

print("="*80)
print("步骤1：筛选符合放贷条件的企业（排除D级企业）")
print("="*80)

# 读取企业基本信息
df_basic = pd.read_csv('1.csv')
print(f"原始企业总数: {len(df_basic)}")

# 分析信誉评级分布
print("\n原始信誉评级分布:")
original_dist = df_basic['信誉评级'].value_counts().sort_index()
for rating, count in original_dist.items():
    pct = count / len(df_basic) * 100
    defaults = len(df_basic[(df_basic['信誉评级'] == rating) & (df_basic['是否违约'] == '是')])
    print(f"{rating}级: {count:>2}家 ({pct:>5.1f}%), 违约{defaults}家")

# 排除D级企业
print(f"\n根据题目要求，排除信誉评级为D的企业:")
d_enterprises = df_basic[df_basic['信誉评级'] == 'D']
print(f"D级企业数量: {len(d_enterprises)}家")
print(f"D级企业违约率: {len(d_enterprises[d_enterprises['是否违约'] == '是']) / len(d_enterprises) * 100:.1f}%")

# 保留符合条件的企业
eligible_enterprises = df_basic[df_basic['信誉评级'] != 'D'].copy().reset_index(drop=True)
print(f"\n符合放贷条件的企业数量: {len(eligible_enterprises)}")

print("\n符合条件企业的信誉评级分布:")
eligible_dist = eligible_enterprises['信誉评级'].value_counts().sort_index()
for rating, count in eligible_dist.items():
    pct = count / len(eligible_enterprises) * 100
    defaults = len(eligible_enterprises[(eligible_enterprises['信誉评级'] == rating) & (eligible_enterprises['是否违约'] == '是')])
    default_rate = defaults / count * 100 if count > 0 else 0
    print(f"{rating}级: {count:>2}家 ({pct:>5.1f}%), 违约{defaults}家 ({default_rate:.1f}%)")

# 保存符合条件的企业名单
eligible_enterprises.to_csv('符合条件企业名单.csv', index=False, encoding='utf-8-sig')
print(f"\n✅ 符合条件的企业名单已保存至: 符合条件企业名单.csv")

print(f"\n总结:")
print(f"• 排除企业: {len(d_enterprises)}家 D级企业")
print(f"• 保留企业: {len(eligible_enterprises)}家 (A级{len(eligible_enterprises[eligible_enterprises['信誉评级']=='A'])}家 + B级{len(eligible_enterprises[eligible_enterprises['信誉评级']=='B'])}家 + C级{len(eligible_enterprises[eligible_enterprises['信誉评级']=='C'])}家)")
print(f"• 符合条件企业的整体违约率: {len(eligible_enterprises[eligible_enterprises['是否违约'] == '是']) / len(eligible_enterprises) * 100:.1f}%")
