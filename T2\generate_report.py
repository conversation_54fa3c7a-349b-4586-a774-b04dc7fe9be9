"""
T2企业信贷分配优化结果分析报告
基于NSGA-II多目标优化算法
"""

import pandas as pd
import numpy as np

def generate_analysis_report():
    """生成详细分析报告"""
    
    print("="*80)
    print("📊 T2企业信贷分配优化结果分析报告")
    print("="*80)
    
    try:
        # 读取结果文件
        pareto_summary = pd.read_csv('T2_Pareto最优解汇总.csv')
        allocation_detail = pd.read_csv('T2_推荐信贷分配方案.csv')
        
        print(f"📈 优化算法性能:")
        print(f"   算法类型: NSGA-II多目标遗传算法")
        print(f"   目标函数: 期望收益最大化 + 风险最小化")
        print(f"   找到Pareto解数量: {len(pareto_summary)}个")
        print(f"   推荐方案放贷企业: {len(allocation_detail)}家")
        
        # 核心指标分析
        print(f"\n💰 核心财务指标:")
        total_budget = 100_000_000
        recommended = pareto_summary.loc[pareto_summary['总收益(元)'] / (pareto_summary['总风险(元)'] + 1) == 
                                        (pareto_summary['总收益(元)'] / (pareto_summary['总风险(元)'] + 1)).max()].iloc[0]
        
        utilization_rate = recommended['总放贷额(元)'] / total_budget
        roi = recommended['总收益(元)'] / recommended['总放贷额(元)']
        risk_rate = recommended['总风险(元)'] / recommended['总放贷额(元)']
        
        print(f"   总预算: {total_budget:,.0f}元")
        print(f"   实际放贷: {recommended['总放贷额(元)']:,.0f}元")
        print(f"   预算利用率: {utilization_rate:.1%}")
        print(f"   预期总收益: {recommended['总收益(元)']:,.0f}元")
        print(f"   预期总风险: {recommended['总风险(元)']:,.0f}元")
        print(f"   投资回报率: {roi:.2%}")
        print(f"   风险损失率: {risk_rate:.2%}")
        print(f"   收益风险比: {recommended['总收益(元)'] / recommended['总风险(元)']:.2f}")
        
        # 资金分配分析
        print(f"\n🏢 资金分配策略:")
        total_loan = recommended['总放贷额(元)']
        
        ratings_allocation = {
            'A级': {
                'amount': recommended['A级分配(元)'],
                'count': recommended['A级企业数'],
                'percentage': recommended['A级分配(元)'] / total_loan
            },
            'B级': {
                'amount': recommended['B级分配(元)'], 
                'count': recommended['B级企业数'],
                'percentage': recommended['B级分配(元)'] / total_loan
            },
            'C级': {
                'amount': recommended['C级分配(元)'],
                'count': recommended['C级企业数'],
                'percentage': recommended['C级分配(元)'] / total_loan
            }
        }
        
        for rating, info in ratings_allocation.items():
            avg_loan = info['amount'] / info['count'] if info['count'] > 0 else 0
            print(f"   {rating}企业: {info['amount']:,.0f}元 ({info['percentage']:.1%}) "
                  f"| {info['count']}家 | 平均{avg_loan:,.0f}元/家")
        
        # 利率策略分析
        print(f"\n📊 利率策略分析:")
        print(f"   加权平均利率: {recommended['平均利率(%)']:.2f}%")
        
        if len(allocation_detail) > 0:
            rating_rates = allocation_detail.groupby('信誉评级').agg({
                '贷款利率(%)': ['mean', 'min', 'max', 'count'],
                '贷款金额(元)': 'sum'
            }).round(2)
            
            for rating in ['A', 'B', 'C']:
                if rating in rating_rates.index:
                    rate_info = rating_rates.loc[rating]
                    avg_rate = rate_info[('贷款利率(%)', 'mean')]
                    min_rate = rate_info[('贷款利率(%)', 'min')]
                    max_rate = rate_info[('贷款利率(%)', 'max')]
                    count = rate_info[('贷款利率(%)', 'count')]
                    
                    print(f"   {rating}级利率: {avg_rate:.2f}% (范围:{min_rate:.2f}%-{max_rate:.2f}%, {count}家)")
        
        # 风险控制分析
        print(f"\n⚠️ 风险控制评估:")
        if len(allocation_detail) > 0:
            avg_churn = allocation_detail['预期流失率(%)'].mean()
            max_churn = allocation_detail['预期流失率(%)'].max()
            min_churn = allocation_detail['预期流失率(%)'].min()
            
            high_risk_count = len(allocation_detail[allocation_detail['预期流失率(%)'] > 30])
            medium_risk_count = len(allocation_detail[(allocation_detail['预期流失率(%)'] >= 15) & 
                                                     (allocation_detail['预期流失率(%)'] <= 30)])
            low_risk_count = len(allocation_detail[allocation_detail['预期流失率(%)'] < 15])
            
            print(f"   平均流失率: {avg_churn:.1f}%")
            print(f"   流失率区间: {min_churn:.1f}% - {max_churn:.1f}%")
            print(f"   风险等级分布:")
            print(f"     低风险(<15%): {low_risk_count}家企业")
            print(f"     中等风险(15%-30%): {medium_risk_count}家企业")
            print(f"     高风险(>30%): {high_risk_count}家企业")
        
        # Pareto前沿分析
        print(f"\n🎯 Pareto前沿解决方案:")
        pareto_summary['收益风险比'] = pareto_summary['总收益(元)'] / (pareto_summary['总风险(元)'] + 1)
        
        # 识别特征解
        conservative_idx = pareto_summary['总风险(元)'].idxmin()
        aggressive_idx = pareto_summary['总收益(元)'].idxmax()
        balanced_idx = pareto_summary['收益风险比'].idxmax()
        
        strategies = {
            '保守策略（最小风险）': pareto_summary.iloc[conservative_idx],
            '均衡策略（最佳收益风险比）': pareto_summary.iloc[balanced_idx],
            '激进策略（最大收益）': pareto_summary.iloc[aggressive_idx]
        }
        
        for name, strategy in strategies.items():
            print(f"\n   {name}:")
            print(f"     预期收益: {strategy['总收益(元)']:,.0f}元")
            print(f"     预期风险: {strategy['总风险(元)']:,.0f}元")
            print(f"     收益风险比: {strategy['收益风险比']:.2f}")
            print(f"     平均利率: {strategy['平均利率(%)']:.2f}%")
            print(f"     放贷企业: {strategy['放贷企业数']}家")
            print(f"     资金利用: {strategy['总放贷额(元)']/total_budget:.1%}")
        
        # 决策建议
        print(f"\n💡 决策建议:")
        best_solution = pareto_summary.iloc[balanced_idx]
        
        print(f"   🎯 推荐采用均衡策略（解{best_solution['解ID']}）:")
        print(f"     - 该方案在收益和风险之间达到最佳平衡")
        print(f"     - 预期年化收益率: {roi:.2%}")
        print(f"     - 风险损失率控制在: {risk_rate:.2%}")
        print(f"     - 资金充分利用，预算利用率: {utilization_rate:.1%}")
        
        print(f"\n   📋 实施要点:")
        print(f"     1. 重点向A级企业({recommended['A级企业数']}家)投放{recommended['A级分配(元)']/1000000:.1f}百万元")
        print(f"     2. 适度支持B级企业({recommended['B级企业数']}家)，分配{recommended['B级分配(元)']/1000000:.1f}百万元")
        print(f"     3. 谨慎投放C级企业({recommended['C级企业数']}家)，控制在{recommended['C级分配(元)']/1000000:.1f}百万元")
        print(f"     4. 严格控制高风险企业投放，优先考虑低流失率客户")
        print(f"     5. 定期监控客户行为，及时调整利率策略")
        
        # 与传统方法对比
        print(f"\n📈 与传统方法对比:")
        traditional_return = total_budget * 0.06  # 假设传统方法6%固定利率
        traditional_risk = total_budget * 0.3     # 假设传统方法30%风险率
        
        return_improvement = (recommended['总收益(元)'] - traditional_return) / traditional_return
        risk_reduction = (traditional_risk - recommended['总风险(元)']) / traditional_risk
        
        print(f"   传统方法(假设): 收益{traditional_return:,.0f}元, 风险{traditional_risk:,.0f}元")
        print(f"   优化后方案: 收益{recommended['总收益(元)']:,.0f}元, 风险{recommended['总风险(元)']:,.0f}元")
        print(f"   收益提升: {return_improvement:.1%}")
        print(f"   风险降低: {risk_reduction:.1%}")
        
        print(f"\n✅ 报告生成完成！")
        
        # 保存报告为文本文件
        with open('T2_优化结果分析报告.txt', 'w', encoding='utf-8') as f:
            f.write("T2企业信贷分配优化结果分析报告\n")
            f.write("="*50 + "\n\n")
            f.write(f"算法类型: NSGA-II多目标遗传算法\n")
            f.write(f"优化目标: 期望收益最大化 + 风险最小化\n")
            f.write(f"解决方案数: {len(pareto_summary)}个Pareto最优解\n\n")
            
            f.write("推荐方案核心指标:\n")
            f.write(f"预期收益: {recommended['总收益(元)']:,.0f}元\n")
            f.write(f"预期风险: {recommended['总风险(元)']:,.0f}元\n")
            f.write(f"收益风险比: {recommended['总收益(元)'] / recommended['总风险(元)']:.2f}\n")
            f.write(f"投资回报率: {roi:.2%}\n")
            f.write(f"风险损失率: {risk_rate:.2%}\n")
            f.write(f"预算利用率: {utilization_rate:.1%}\n")
            f.write(f"放贷企业数: {recommended['放贷企业数']}家\n")
        
        return True
        
    except FileNotFoundError as e:
        print(f"❌ 错误: 找不到结果文件 {e}")
        print("请先运行优化算法生成结果文件")
        return False
    except Exception as e:
        print(f"❌ 分析过程出错: {e}")
        return False

if __name__ == "__main__":
    success = generate_analysis_report()
    
    if success:
        print("\n📄 详细分析报告已保存为: T2_优化结果分析报告.txt")
    
    input("\n按Enter键退出...")
