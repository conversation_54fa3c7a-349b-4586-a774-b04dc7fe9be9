"""
基于正确的八项指标使用PCA方法制定信贷投放策略
"""
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
import matplotlib.pyplot as plt

print("="*80)
print("基于PCA方法的信贷投放策略（使用正确的八项指标）")
print("="*80)

# 读取正确的标准化八项指标数据
standardized_data = pd.read_csv('正确的标准化八项指标.csv')
print(f"参与PCA分析的企业数量: {len(standardized_data)}家")

# 读取符合条件企业的信誉评级信息
eligible_enterprises = pd.read_csv('符合条件企业名单.csv')

# 提取八项指标数据进行PCA
indicators = ['负债水平', '盈利能力', '现金流稳定性', '企业规模', 
             '税负压力', '公司市场竞争力', '盈利预测可靠性', '经营风险']

pca_input_data = standardized_data[indicators].values
enterprise_codes = standardized_data['企业代号'].values

print(f"PCA输入数据形状: {pca_input_data.shape}")
print(f"八项指标: {indicators}")

# 执行PCA分析
print(f"\n正在执行PCA分析...")
pca = PCA(n_components=8)
pca_result = pca.fit_transform(pca_input_data)

# 分析主成分解释方差比
print(f"\n✅ PCA主成分分析结果:")
explained_variance_ratio = pca.explained_variance_ratio_
cumulative_variance = np.cumsum(explained_variance_ratio)

for i in range(8):
    print(f"PC{i+1}: 解释方差比={explained_variance_ratio[i]:.4f} ({explained_variance_ratio[i]*100:.2f}%), "
          f"累计解释方差比={cumulative_variance[i]:.4f} ({cumulative_variance[i]*100:.2f}%)")

# 选择主成分数量（累计解释方差比达到80%以上）
n_components = np.where(cumulative_variance >= 0.80)[0][0] + 1
print(f"\n选择前{n_components}个主成分（累计解释方差比: {cumulative_variance[n_components-1]:.4f}）")

# 计算PCA综合得分
# 使用前n个主成分，按其解释方差比加权
pca_scores = np.zeros(len(enterprise_codes))
for i in range(n_components):
    pca_scores += pca_result[:, i] * explained_variance_ratio[i]

# 标准化PCA得分到[0,1]区间
pca_scores_normalized = (pca_scores - pca_scores.min()) / (pca_scores.max() - pca_scores.min())

# 创建PCA结果DataFrame
pca_results = pd.DataFrame({
    '企业代号': enterprise_codes,
    'PCA综合得分': pca_scores_normalized
})

# 排序并生成排名
pca_results = pca_results.sort_values('PCA综合得分', ascending=False).reset_index(drop=True)
pca_results['PCA排名'] = range(1, len(pca_results) + 1)

# 添加信誉评级信息
pca_results = pd.merge(pca_results, eligible_enterprises[['企业代号', '信誉评级']], 
                      on='企业代号', how='left')

print(f"\n✅ PCA综合得分统计:")
print(f"最高分: {pca_results['PCA综合得分'].max():.4f}")
print(f"最低分: {pca_results['PCA综合得分'].min():.4f}")
print(f"平均分: {pca_results['PCA综合得分'].mean():.4f}")

# 基于PCA得分分配利率（4%-15%区间）
def assign_pca_interest_rate(score, max_score, min_score):
    """基于PCA综合得分线性分配利率"""
    normalized_score = (score - min_score) / (max_score - min_score)
    # 得分高 -> 利率低
    interest_rate = 4.0 + (1 - normalized_score) * 11.0
    return round(interest_rate, 2)

max_pca_score = pca_results['PCA综合得分'].max()
min_pca_score = pca_results['PCA综合得分'].min()

pca_results['建议利率(%)'] = pca_results['PCA综合得分'].apply(
    lambda x: assign_pca_interest_rate(x, max_pca_score, min_pca_score)
)

# 添加风险等级
def assign_pca_risk_level(rate):
    if rate <= 7.0:
        return "低风险"
    elif rate <= 11.0:
        return "中风险"
    else:
        return "高风险"

pca_results['风险等级'] = pca_results['建议利率(%)'].apply(assign_pca_risk_level)
pca_results['建议放贷'] = '是'

# 策略统计分析
print(f"\n✅ PCA策略统计:")
print(f"利率范围: {pca_results['建议利率(%)'].min():.2f}% - {pca_results['建议利率(%)'].max():.2f}%")
print(f"平均利率: {pca_results['建议利率(%)'].mean():.2f}%")

# 风险等级统计
risk_stats = pca_results.groupby('风险等级').agg({
    '企业代号': 'count',
    '建议利率(%)': ['mean', 'min', 'max']
}).round(2)
risk_stats.columns = ['企业数量', '平均利率', '最低利率', '最高利率']
print(f"\n按风险等级统计:")
print(risk_stats)

# 信誉评级统计
rating_stats = pca_results.groupby('信誉评级').agg({
    '企业代号': 'count',
    '建议利率(%)': ['mean', 'min', 'max'],
    'PCA综合得分': ['mean', 'min', 'max']
}).round(4)
rating_stats.columns = ['企业数量', '平均利率', '最低利率', '最高利率', '平均得分', '最低得分', '最高得分']
print(f"\n按信誉评级统计:")
print(rating_stats)

# 显示PCA策略结果
print(f"\n🎯 PCA方法信贷策略 - 前20名企业:")
print("排名 | 企业 | PCA得分 | 信誉评级 | 风险等级 | 建议利率")
print("-" * 58)
for idx, row in pca_results.head(20).iterrows():
    print(f"{row['PCA排名']:>3d} | {row['企业代号']:>4s} | {row['PCA综合得分']:7.4f} | {row['信誉评级']:>6s} | {row['风险等级']:>4s} | {row['建议利率(%)']:6.2f}%")

print(f"\n🎯 PCA方法信贷策略 - 后10名企业:")
print("排名 | 企业 | PCA得分 | 信誉评级 | 风险等级 | 建议利率")
print("-" * 58)
for idx, row in pca_results.tail(10).iterrows():
    print(f"{row['PCA排名']:>3d} | {row['企业代号']:>4s} | {row['PCA综合得分']:7.4f} | {row['信誉评级']:>6s} | {row['风险等级']:>4s} | {row['建议利率(%)']:6.2f}%")

# 保存PCA策略结果
pca_final_strategy = pca_results[['企业代号', 'PCA综合得分', 'PCA排名', '信誉评级',
                                 '风险等级', '建议利率(%)', '建议放贷']].copy()

pca_final_strategy.to_csv('PCA方法信贷投放策略.csv', index=False, encoding='utf-8-sig')
print(f"\n✅ PCA策略已保存至: PCA方法信贷投放策略.csv")

# 主成分载荷分析
print(f"\n✅ 主成分载荷分析（前{n_components}个主成分）:")
components = pca.components_[:n_components]
for i in range(n_components):
    print(f"\nPC{i+1} (解释方差比: {explained_variance_ratio[i]:.4f}):")
    loadings = components[i]
    sorted_indices = np.argsort(np.abs(loadings))[::-1]
    for j in sorted_indices:
        print(f"  {indicators[j]:15s}: {loadings[j]:7.4f}")

# 与AHP方法对比
print(f"\n✅ PCA vs AHP方法对比:")
ahp_results = pd.read_csv('正确的AHP综合评估结果.csv')

# 合并两种方法的结果进行对比
comparison = pd.merge(
    pca_results[['企业代号', 'PCA综合得分', 'PCA排名']],
    ahp_results[['企业代号', 'AHP综合得分', 'AHP排名']],
    on='企业代号'
)

correlation_scores = comparison['PCA综合得分'].corr(comparison['AHP综合得分'])
correlation_rankings = comparison['PCA排名'].corr(comparison['AHP排名'])

print(f"得分相关性: {correlation_scores:.4f}")
print(f"排名相关性: {correlation_rankings:.4f}")

# 显示排名差异较大的企业
comparison['排名差异'] = abs(comparison['PCA排名'] - comparison['AHP排名'])
print(f"\n排名差异最大的10家企业:")
top_diff = comparison.nlargest(10, '排名差异')
print("企业代号 | PCA排名 | AHP排名 | 排名差异")
print("-" * 35)
for _, row in top_diff.iterrows():
    print(f"{row['企业代号']:>6s} | {row['PCA排名']:>6d} | {row['AHP排名']:>6d} | {row['排名差异']:>6d}")

print(f"\n" + "="*80)
print("🎉 PCA方法信贷决策分析完成！")
print(f"✅ 使用前{n_components}个主成分（累计解释方差比{cumulative_variance[n_components-1]:.2%}）")
print("✅ 99家符合条件企业获得4.00%-15.00%的差异化利率")
print(f"✅ 与AHP方法的排名相关性: {correlation_rankings:.4f}")
print("="*80)
