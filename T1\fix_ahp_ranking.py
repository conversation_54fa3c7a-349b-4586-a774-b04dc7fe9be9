"""
修正AHP排名错误，重新生成正确的AHP信贷分配策略
"""
import pandas as pd
import numpy as np
from scipy.stats import rankdata
import warnings
warnings.filterwarnings('ignore')

print("="*80)
print("修正AHP排名错误 - 重新生成正确的AHP信贷分配策略")
print("="*80)

# 读取原始数据
df = pd.read_csv('AHP层次分析法信贷分配策略.csv', encoding='gbk')

print("1. 问题诊断")
print("="*50)
print(f"发现问题：当前AHP排名与AHP综合得分不匹配")
print(f"• 数据总量：{len(df)}家企业")
print(f"• 得分最高企业：{df.loc[df['AHP综合得分'].idxmax(), '企业代号']} (得分:{df['AHP综合得分'].max():.6f}, 当前排名:{df.loc[df['AHP综合得分'].idxmax(), 'AHP排名']})")
print(f"• 排名第1企业：{df.loc[df['AHP排名'] == 1, '企业代号'].values[0]} (得分:{df.loc[df['AHP排名'] == 1, 'AHP综合得分'].values[0]:.6f})")

print("\n2. 重新计算正确排名")
print("="*50)

# 计算正确的AHP排名
scores = df['AHP综合得分'].values
correct_ranks = rankdata(-scores, method='min').astype(int)

# 更新数据框
df_corrected = df.copy()
df_corrected['原始错误排名'] = df_corrected['AHP排名']
df_corrected['AHP排名'] = correct_ranks

print(f"✅ 排名修正完成")

print("\n3. 验证修正结果")
print("="*50)
print("修正前后对比（前10名）:")
print("企业代号 | AHP得分   | 错误排名 | 正确排名")
print("-"*45)

df_top10 = df_corrected.nlargest(10, 'AHP综合得分')
for _, row in df_top10.iterrows():
    print(f'{row["企业代号"]:>6} | {row["AHP综合得分"]:>8.6f} | {row["原始错误排名"]:>6} | {row["AHP排名"]:>6}')

print("\n4. 重新计算风险等级分配")
print("="*50)

# 基于正确排名重新计算风险等级分配
def recalculate_allocation_with_correct_ranking(df):
    """基于正确的AHP排名重新计算分配策略"""
    n_companies = len(df)
    
    # 重新定义风险等级（基于正确排名）
    def get_risk_level_by_rank(rank, total):
        if rank <= total * 0.1:  # 前10%
            return '极高风险'
        elif rank <= total * 0.3:  # 前30%
            return '高风险'
        elif rank <= total * 0.7:  # 前70%
            return '中等风险'
        else:
            return '低风险'
    
    # 重新分配风险系数
    risk_coefficients = {
        '低风险': 1.0,
        '中等风险': 0.8,
        '高风险': 0.5,
        '极高风险': 0.1
    }
    
    # 计算新的风险等级和分配
    df['新风险等级'] = df['AHP排名'].apply(lambda x: get_risk_level_by_rank(x, n_companies))
    df['新风险系数'] = df['新风险等级'].map(risk_coefficients)
    
    # 重新计算基础分配（基于AHP得分）
    base_allocation = df['AHP综合得分'] / df['AHP综合得分'].sum()
    
    # 应用风险调整
    adjusted_allocation = base_allocation * df['新风险系数']
    final_allocation = adjusted_allocation / adjusted_allocation.sum()  # 重新归一化
    
    df['新基础分配比例(%)'] = base_allocation * 100
    df['新最终分配比例(%)'] = final_allocation * 100
    
    return df

# 重新计算分配
df_corrected = recalculate_allocation_with_correct_ranking(df_corrected)

print("✅ 分配策略重新计算完成")

print("\n5. 新旧分配对比")
print("="*50)
print("风险等级分布对比:")
old_risk_dist = df_corrected['风险等级'].value_counts().sort_index()
new_risk_dist = df_corrected['新风险等级'].value_counts().sort_index()

for risk in ['低风险', '中等风险', '高风险', '极高风险']:
    old_count = old_risk_dist.get(risk, 0)
    new_count = new_risk_dist.get(risk, 0)
    print(f'{risk:>8}: 原分布 {old_count:>3}家 → 新分布 {new_count:>3}家')

print("\n6. 保存修正结果")
print("="*50)

# 整理最终输出字段
final_columns = ['企业代号', 'AHP综合得分', 'AHP排名', '新最终分配比例(%)', '新风险等级']
df_final = df_corrected[final_columns].copy()
df_final.columns = ['企业代号', 'AHP综合得分', 'AHP排名', '最终分配比例(%)', '风险等级']

# 按排名排序
df_final = df_final.sort_values('AHP排名')

# 保存修正后的结果
df_final.to_csv('AHP层次分析法信贷分配策略_修正版.csv', index=False, encoding='utf-8-sig')
print("✅ 修正版策略已保存至: AHP层次分析法信贷分配策略_修正版.csv")

# 也保存包含对比信息的详细版本
df_corrected.to_csv('AHP排名修正对比详情.csv', index=False, encoding='utf-8-sig')
print("✅ 修正对比详情已保存至: AHP排名修正对比详情.csv")

print("\n7. 修正后的前20名企业")
print("="*80)
print("排名 | 企业代号 | AHP得分   | 分配比例(%) | 风险等级")
print("-"*60)

for _, row in df_final.head(20).iterrows():
    print(f'{row["AHP排名"]:>3} | {row["企业代号"]:>6} | {row["AHP综合得分"]:>8.6f} | {row["最终分配比例(%)"]:>8.4f} | {row["风险等级"]}')

print("\n8. 修正总结")
print("="*50)
print("🎯 修正内容:")
print("1. ✅ AHP排名：现在完全基于AHP综合得分计算")
print("2. ✅ 风险等级：基于正确排名重新分配")
print("3. ✅ 分配比例：基于正确的风险等级重新计算")
print()
print("📊 修正效果:")
print(f"• 得分最高企业 {df_final.iloc[0]['企业代号']} 现在排名第1")
print(f"• 前10%企业({int(len(df)*0.1)}家)被正确识别为极高风险")
print(f"• 分配逻辑：得分高→排名高→风险低→分配多")
print()
print("⚠️ 重要提醒:")
print("请使用修正版文件重新运行后续的利率分配策略！")
