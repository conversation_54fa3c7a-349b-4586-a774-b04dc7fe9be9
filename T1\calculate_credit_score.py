import pandas as pd
import numpy as np

# 读取企业基本信息
print("正在读取企业基本信息...")
company_df = pd.read_csv('1.csv')

print(f"总企业数量: {len(company_df)}")

# 定义打分函数
def calculate_credit_score(row):
    # 基础分数根据信誉评级
    credit_rating = row['信誉评级']
    if credit_rating == 'A':
        base_score = 90
    elif credit_rating == 'B':
        base_score = 70
    elif credit_rating == 'C':
        base_score = 50
    elif credit_rating == 'D':
        base_score = 0  # D级企业原则上不予放贷，给0分
    else:
        base_score = 0  # 其他情况给0分
    
    # 违约扣分
    is_default = row['是否违约']
    if is_default == '是':
        penalty = 50
    else:
        penalty = 0
    
    # 计算最终分数（不能低于0分）
    final_score = max(0, base_score - penalty)
    
    return final_score, base_score, penalty

# 计算每个企业的分数
print("正在计算企业信用分数...")
company_df['基础分数'] = 0
company_df['违约扣分'] = 0
company_df['最终信用分数'] = 0

for idx, row in company_df.iterrows():
    final_score, base_score, penalty = calculate_credit_score(row)
    company_df.at[idx, '基础分数'] = base_score
    company_df.at[idx, '违约扣分'] = penalty
    company_df.at[idx, '最终信用分数'] = final_score

# 显示结果
print("\n企业信用评分结果示例:")
print(company_df[['企业代号', '信誉评级', '是否违约', '基础分数', '违约扣分', '最终信用分数']].head(15))

# 统计分析
print(f"\n=== 信用评分统计分析 ===")
print(f"平均分数: {company_df['最终信用分数'].mean():.2f}")
print(f"最高分数: {company_df['最终信用分数'].max()}")
print(f"最低分数: {company_df['最终信用分数'].min()}")
print(f"标准差: {company_df['最终信用分数'].std():.2f}")

# 按信誉评级分析
print(f"\n=== 按信誉评级分析 ===")
rating_stats = company_df.groupby('信誉评级').agg({
    '最终信用分数': ['count', 'mean', 'min', 'max'],
    '是否违约': lambda x: (x == '是').sum()
}).round(2)
print(rating_stats)

# 违约情况分析
print(f"\n=== 违约情况分析 ===")
default_stats = company_df.groupby('是否违约').agg({
    '最终信用分数': ['count', 'mean', 'min', 'max'],
    '信誉评级': lambda x: x.value_counts().to_dict()
}).round(2)
print(default_stats)

# 分数分布
print(f"\n=== 分数分布 ===")
score_distribution = company_df['最终信用分数'].value_counts().sort_index()
print(score_distribution)

# 保存结果
result_df = company_df[['企业代号', '企业名称', '信誉评级', '是否违约', '基础分数', '违约扣分', '最终信用分数']]
result_df.to_csv('企业信用评分结果.csv', index=False, encoding='utf-8-sig')
print(f"\n结果已保存到: 企业信用评分结果.csv")

# 高风险企业（低分企业）
print(f"\n=== 高风险企业（分数≤20分）===")
high_risk = company_df[company_df['最终信用分数'] <= 20]
if len(high_risk) > 0:
    print(high_risk[['企业代号', '企业名称', '信誉评级', '是否违约', '最终信用分数']])
else:
    print("无高风险企业")

# 优质企业（高分企业）
print(f"\n=== 优质企业（分数≥80分）===")
high_quality = company_df[company_df['最终信用分数'] >= 80]
if len(high_quality) > 0:
    print(f"优质企业数量: {len(high_quality)}家")
    print(high_quality[['企业代号', '企业名称', '信誉评级', '是否违约', '最终信用分数']].head(10))
else:
    print("无优质企业")

print(f"\n=== 各分数段企业分布 ===")
bins = [0, 20, 40, 60, 80, 100]
labels = ['0-20分(高风险)', '21-40分(中高风险)', '41-60分(中等风险)', '61-80分(中低风险)', '81-100分(低风险)']
company_df['风险等级'] = pd.cut(company_df['最终信用分数'], bins=bins, labels=labels, include_lowest=True)
risk_distribution = company_df['风险等级'].value_counts()
print(risk_distribution)
