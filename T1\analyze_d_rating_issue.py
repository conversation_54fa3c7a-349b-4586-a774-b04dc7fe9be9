"""
分析题目要求：信誉评级为D的企业不予贷款
检查当前数据中D级企业的情况
"""
import pandas as pd

print("="*80)
print("题目要求分析：信誉评级为D的企业原则上不予放贷")
print("="*80)

# 读取企业基本信息
df_basic = pd.read_csv('1.csv')

print("1. 企业信誉评级分布")
print("="*50)
credit_rating_dist = df_basic['信誉评级'].value_counts().sort_index()
print("信誉评级分布:")
for rating, count in credit_rating_dist.items():
    pct = count / len(df_basic) * 100
    print(f"{rating}级: {count:>3}家 ({pct:>5.1f}%)")

print(f"\n总企业数: {len(df_basic)}家")

print("\n2. 违约情况与信誉评级关系")
print("="*50)
default_analysis = df_basic.groupby(['信誉评级', '是否违约']).size().unstack(fill_value=0)
print("违约情况统计:")
print(default_analysis)

# 计算违约率
print("\n各信誉评级违约率:")
for rating in ['A', 'B', 'C', 'D']:
    if rating in default_analysis.index:
        total = default_analysis.loc[rating].sum()
        defaults = default_analysis.loc[rating, '是'] if '是' in default_analysis.columns else 0
        default_rate = defaults / total * 100 if total > 0 else 0
        print(f"{rating}级: {defaults}/{total} = {default_rate:.1f}% 违约率")

print("\n3. D级企业详细信息")
print("="*50)
d_enterprises = df_basic[df_basic['信誉评级'] == 'D']
print(f"D级企业数量: {len(d_enterprises)}家")
print(f"D级企业代号: {list(d_enterprises['企业代号'])}")

print("\nD级企业违约情况:")
d_defaults = d_enterprises['是否违约'].value_counts()
print(d_defaults)

print("\n4. 检查当前AHP策略中是否包含D级企业")
print("="*50)

try:
    # 检查修正版的AHP策略
    df_ahp = pd.read_csv('AHP层次分析法信贷分配策略_最终修正版.csv', encoding='utf-8-sig')
    
    # 合并数据检查D级企业
    df_merged = df_ahp.merge(df_basic[['企业代号', '信誉评级', '是否违约']], on='企业代号', how='left')
    
    d_in_strategy = df_merged[df_merged['信誉评级'] == 'D']
    
    print(f"当前AHP策略包含的企业总数: {len(df_ahp)}")
    print(f"其中D级企业数量: {len(d_in_strategy)}")
    
    if len(d_in_strategy) > 0:
        print(f"❌ 问题：AHP策略包含了{len(d_in_strategy)}家D级企业！")
        print("D级企业在策略中的情况:")
        print("企业代号 | AHP排名 | 分配比例(%) | 风险等级")
        print("-" * 45)
        for _, row in d_in_strategy.iterrows():
            print(f"{row['企业代号']:>6} | {row['AHP排名']:>5} | {row['最终分配比例(%)']:>8.4f} | {row['风险等级']}")
        
        print(f"\nD级企业分配比例总和: {d_in_strategy['最终分配比例(%)'].sum():.4f}%")
    else:
        print("✅ 当前AHP策略没有包含D级企业")
        
except FileNotFoundError:
    print("❌ 未找到AHP策略文件，需要重新生成")

print("\n5. 题目要求总结")
print("="*50)
print("📋 关键要求:")
print("• 银行对信誉评级为D的企业原则上不予放贷")
print("• 需要从所有分析和策略中排除D级企业")
print("• 只对A、B、C级企业进行信贷分配")

eligible_enterprises = df_basic[df_basic['信誉评级'].isin(['A', 'B', 'C'])]
print(f"\n符合放贷条件的企业:")
print(f"• A级企业: {len(df_basic[df_basic['信誉评级'] == 'A'])}家")
print(f"• B级企业: {len(df_basic[df_basic['信誉评级'] == 'B'])}家") 
print(f"• C级企业: {len(df_basic[df_basic['信誉评级'] == 'C'])}家")
print(f"• 合计: {len(eligible_enterprises)}家")

print(f"\n不符合放贷条件的企业:")
print(f"• D级企业: {len(d_enterprises)}家 (需要排除)")

print(f"\n6. 需要重新处理的文件和数据")
print("="*50)
print("需要修改的内容:")
print("1. ✅ 重新筛选企业：只保留A、B、C级企业")
print("2. ✅ 重新计算指标：基于符合条件的企业重新计算")
print("3. ✅ 重新AHP分析：重新计算排名和分配策略") 
print("4. ✅ 重新利率分配：基于新的企业集合重新分配")
print("5. ✅ 更新所有相关文件")

print("\n" + "="*80)
print("结论：必须重新处理所有数据，排除D级企业！")
print("="*80)
