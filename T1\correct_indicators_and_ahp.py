"""
基于正确理解重新构建八项指标体系和AHP综合评估
使用现有的企业四项指标原始数据.csv作为基础四项指标
"""
import pandas as pd
import numpy as np
from sklearn.preprocessing import MinMaxScaler

print("="*80)
print("八项指标体系重新构建 - 基于正确的指标定义")
print("="*80)

# 读取符合条件的企业名单
eligible_enterprises = pd.read_csv('符合条件企业名单.csv')
eligible_company_list = eligible_enterprises['企业代号'].tolist()
print(f"符合条件企业数量: {len(eligible_company_list)}家")

# 读取现有的四项基础指标（包含所有123家企业）
all_basic_indicators = pd.read_csv('企业四项指标原始数据.csv')

# 筛选出符合条件的99家企业的基础指标
basic_indicators = all_basic_indicators[
    all_basic_indicators['企业代号'].isin(eligible_company_list)
].copy()

print(f"筛选后基础指标企业数: {len(basic_indicators)}家")

# 重新命名列为正确的指标名称
basic_indicators.rename(columns={
    '最终信用分数': '负债水平',     # 负债水平 = 信用分数（正向指标）
    '盈利能力': '盈利能力',         # 盈利能力 = 月度差值中位数（正向指标）
    '稳定性': '现金流稳定性',       # 现金流稳定性 = 稳定性指标（正向指标）
    '企业规模': '企业规模'          # 企业规模 = 月度绝对值之和平均值（正向指标）
}, inplace=True)

print("\n✅ 基础四项指标（已重新命名）:")
print("1. 负债水平: 企业信用分数（30-100分），分值越高负债风险越低")
print("2. 盈利能力: 月度差值中位数（销项-进项），数值越大盈利能力越强")
print("3. 现金流稳定性: 基于月度差值标准差的稳定性指标（0-1），越接近1越稳定")
print("4. 企业规模: 月度绝对值之和平均值，数值越大企业规模越大")

# 读取税务四项指标（之前计算的结果应该是正确的）
tax_indicators = pd.read_csv('符合条件企业_税务四项指标.csv')

print(f"\n✅ 税务四项指标:")
print("5. 税负压力: 销项税率 - 进项税率（负向指标，越大压力越大）")
print("6. 公司市场竞争力: 销项总额 / (进项总额 + 1)（正向指标，越大竞争力越强）")
print("7. 盈利预测可靠性: 销项发票数 / 总发票数（正向指标，越大越可靠）")
print("8. 经营风险: 税率差异绝对值 + 发票不平衡系数（负向指标，越大风险越大）")

# 合并基础指标和税务指标
complete_indicators = pd.merge(
    basic_indicators[['企业代号', '负债水平', '盈利能力', '现金流稳定性', '企业规模']],
    tax_indicators,
    on='企业代号',
    how='inner'
)

print(f"\n合并后的完整八项指标企业数: {len(complete_indicators)}家")

# 显示各指标的统计信息
indicators = ['负债水平', '盈利能力', '现金流稳定性', '企业规模', 
             '税负压力', '公司市场竞争力', '盈利预测可靠性', '经营风险']

print(f"\n八项指标统计摘要:")
for indicator in indicators:
    data = complete_indicators[indicator]
    print(f"{indicator:12s}: min={data.min():10.4f}, max={data.max():10.4f}, mean={data.mean():8.4f}")

# 保存完整的八项指标
complete_indicators.to_csv('正确的完整八项指标.csv', index=False, encoding='utf-8-sig')

# 指标标准化处理
indicators_info = {
    '负债水平': {'type': 'positive', 'description': '正向指标 - 信用分数越高越好'},
    '盈利能力': {'type': 'positive', 'description': '正向指标 - 盈利能力越强越好'},
    '现金流稳定性': {'type': 'positive', 'description': '正向指标 - 现金流越稳定越好'},
    '企业规模': {'type': 'positive', 'description': '正向指标 - 企业规模越大越好'},
    '税负压力': {'type': 'negative', 'description': '负向指标 - 税负压力越小越好'},
    '公司市场竞争力': {'type': 'positive', 'description': '正向指标 - 市场竞争力越强越好'},
    '盈利预测可靠性': {'type': 'positive', 'description': '正向指标 - 预测可靠性越高越好'},
    '经营风险': {'type': 'negative', 'description': '负向指标 - 经营风险越小越好'}
}

print(f"\n指标方向性:")
for indicator, info in indicators_info.items():
    print(f"  {indicator}: {info['description']}")

# 标准化处理
standardized_data = pd.DataFrame()
standardized_data['企业代号'] = complete_indicators['企业代号']

scaler = MinMaxScaler()

for indicator, info in indicators_info.items():
    original_data = complete_indicators[indicator].values.reshape(-1, 1)
    
    if info['type'] == 'positive':
        # 正向指标：直接Min-Max标准化
        standardized_values = scaler.fit_transform(original_data).flatten()
    else:
        # 负向指标：取反后标准化
        standardized_values = 1 - scaler.fit_transform(original_data).flatten()
    
    standardized_data[indicator] = standardized_values

# 使用正确的AHP权重（基于层次分析法的权重分配）
ahp_weights = {
    '负债水平': 0.2637,           # 信用风险最重要
    '盈利能力': 0.2637,           # 盈利能力同等重要
    '现金流稳定性': 0.0659,       # 现金流稳定性
    '企业规模': 0.1318,           # 企业规模
    '税负压力': 0.0659,           # 税负压力
    '公司市场竞争力': 0.1318,     # 市场竞争力
    '盈利预测可靠性': 0.0659,     # 盈利预测可靠性
    '经营风险': 0.0114            # 经营风险权重最小
}

print(f"\n✅ AHP权重分配:")
for indicator, weight in ahp_weights.items():
    print(f"  {indicator:12s}: {weight:6.4f}")

total_weight = sum(ahp_weights.values())
print(f"\n权重总和验证: {total_weight:.4f}")

# 计算AHP综合得分
ahp_scores = []
for idx, row in standardized_data.iterrows():
    score = 0
    for indicator in indicators:
        score += row[indicator] * ahp_weights[indicator]
    ahp_scores.append(score)

# 生成最终结果
final_results = standardized_data[['企业代号']].copy()
final_results['AHP综合得分'] = ahp_scores

# 排序并生成排名
final_results = final_results.sort_values('AHP综合得分', ascending=False).reset_index(drop=True)
final_results['AHP排名'] = range(1, len(final_results) + 1)

# 添加信誉评级信息
final_results = pd.merge(final_results, eligible_enterprises[['企业代号', '信誉评级']], 
                        on='企业代号', how='left')

print(f"\n✅ AHP综合得分统计:")
print(f"最高分: {final_results['AHP综合得分'].max():.4f}")
print(f"最低分: {final_results['AHP综合得分'].min():.4f}")
print(f"平均分: {final_results['AHP综合得分'].mean():.4f}")

# 显示排名结果
print(f"\n前20名企业AHP排名:")
print("排名 | 企业代号 | AHP得分 | 信誉评级")
print("-" * 35)
for idx, row in final_results.head(20).iterrows():
    print(f"{row['AHP排名']:>3d} | {row['企业代号']:>6s} | {row['AHP综合得分']:7.4f} | {row['信誉评级']:>6s}")

# 保存标准化数据和最终结果
standardized_data.to_csv('正确的标准化八项指标.csv', index=False, encoding='utf-8-sig')
final_results.to_csv('正确的AHP综合评估结果.csv', index=False, encoding='utf-8-sig')

print(f"\n✅ 文件保存完成:")
print("- 正确的完整八项指标.csv")
print("- 正确的标准化八项指标.csv") 
print("- 正确的AHP综合评估结果.csv")

print(f"\n下一步：基于正确的AHP排名分配利率")
