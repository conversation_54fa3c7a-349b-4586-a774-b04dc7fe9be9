"""
基于层次分析法(AHP)的信贷分配策略
使用指定权重结构：
公司实力0.4 = 盈利能力0.15 + 稳定性0.15 + 企业规模0.05 + 公司市场竞争力0.03 + 盈利预测可靠性0.02
信誉等级0.6 = 最终信用分数0.25 + 经营风险0.25 + 税负压力0.1
"""
import pandas as pd
import numpy as np
from sklearn.preprocessing import MinMaxScaler
import warnings
warnings.filterwarnings('ignore')

print("="*80)
print("基于层次分析法(AHP)的信贷分配策略")
print("="*80)

# 读取原始数据
data_enterprise = pd.read_csv('企业四项指标原始数据.csv')
data_input = pd.read_csv('2.csv')  # 进项发票数据
data_output = pd.read_csv('3.csv')  # 销项发票数据

print("1. 计算税务指标（仅有效发票）")
print("="*50)

# 为每个企业计算税务指标
def calculate_tax_indicators():
    """计算税务相关指标（仅考虑有效发票）"""
    tax_indicators = {}
    
    for company in data_enterprise['企业代号']:
        # 获取该企业的进项和销项数据，只考虑有效发票
        input_data = data_input[(data_input['企业代号'] == company) & (data_input['发票状态'] == '有效发票')]
        output_data = data_output[(data_output['企业代号'] == company) & (data_output['发票状态'] == '有效发票')]
        
        if len(input_data) == 0 or len(output_data) == 0:
            # 如果没有数据，设为0
            tax_indicators[company] = {
                '税负压力': 0,
                '公司市场竞争力': 0,
                '盈利预测可靠性': 0,
                '经营风险': 0
            }
            continue
        
        # 计算进项税率和销项税率
        input_tax_rate = input_data['税额'].sum() / input_data['金额'].sum() if input_data['金额'].sum() > 0 else 0
        output_tax_rate = output_data['税额'].sum() / output_data['金额'].sum() if output_data['金额'].sum() > 0 else 0
        
        # 1. 税负压力：销项税率 - 进项税率（负向指标，越大压力越大）
        tax_pressure = output_tax_rate - input_tax_rate
        
        # 2. 公司市场竞争力：销项税总额（正向指标，越高竞争力越强）
        market_competitiveness = output_data['税额'].sum()
        
        # 3. 盈利预测可靠性：月度销项税稳定性（正向指标，越稳定越好）
        output_data_copy = output_data.copy()
        output_data_copy['开票日期'] = pd.to_datetime(output_data_copy['开票日期'])
        output_data_copy['年月'] = output_data_copy['开票日期'].dt.to_period('M')
        
        monthly_output_tax = output_data_copy.groupby('年月')['税额'].sum()
        if len(monthly_output_tax) > 1:
            cv = monthly_output_tax.std() / monthly_output_tax.mean() if monthly_output_tax.mean() > 0 else float('inf')
            profit_reliability = 1 / (1 + cv)  # 转换为正向指标
        else:
            profit_reliability = 0.5  # 中等值
        
        # 4. 经营风险：销项税率 - 进项税率（负向指标，越高风险越大）
        operating_risk = output_tax_rate - input_tax_rate
        
        tax_indicators[company] = {
            '税负压力': tax_pressure,
            '公司市场竞争力': market_competitiveness,
            '盈利预测可靠性': profit_reliability,
            '经营风险': operating_risk
        }
    
    return tax_indicators

# 计算税务指标
print("正在计算各企业税务指标...")
tax_indicators = calculate_tax_indicators()

# 转换为DataFrame
tax_df = pd.DataFrame.from_dict(tax_indicators, orient='index')
tax_df.reset_index(inplace=True)
tax_df.rename(columns={'index': '企业代号'}, inplace=True)

print(f"税务指标计算完成，统计描述：")
print(tax_df[['税负压力', '公司市场竞争力', '盈利预测可靠性', '经营风险']].describe().round(4))

print(f"\n2. 合并所有指标数据")
print("="*50)

# 合并原有指标和新增指标
merged_data = pd.merge(data_enterprise, tax_df, on='企业代号', how='left')
merged_data = merged_data.fillna(0)

print(f"合并后数据维度: {merged_data.shape}")

# 负向指标转换（税负压力和经营风险需要反向处理）
merged_data['税负压力_反向'] = -merged_data['税负压力']  # 压力越大越不好
merged_data['经营风险_反向'] = -merged_data['经营风险']  # 风险越大越不好

print(f"\n3. 数据0-1标准化")
print("="*50)

# 定义需要标准化的指标（注意正负向）
indicators_raw = {
    '最终信用分数': merged_data['最终信用分数'].values,
    '盈利能力': merged_data['盈利能力'].values,
    '企业规模': merged_data['企业规模'].values,
    '稳定性': merged_data['稳定性'].values,
    '公司市场竞争力': merged_data['公司市场竞争力'].values,
    '盈利预测可靠性': merged_data['盈利预测可靠性'].values,
    '税负压力_反向': merged_data['税负压力_反向'].values,
    '经营风险_反向': merged_data['经营风险_反向'].values
}

# 0-1标准化
scaler = MinMaxScaler()
standardized_indicators = {}

for indicator_name, values in indicators_raw.items():
    values_reshaped = values.reshape(-1, 1)
    standardized_values = scaler.fit_transform(values_reshaped).flatten()
    standardized_indicators[indicator_name] = standardized_values

# 创建标准化数据框
standardized_df = pd.DataFrame(standardized_indicators)
standardized_df['企业代号'] = merged_data['企业代号'].values

print("标准化后数据统计:")
print(standardized_df.describe().round(4))

print(f"\n4. AHP权重设置")
print("="*50)

# AHP层次权重结构
print("权重结构：")
print("总目标 = 公司实力(0.4) + 信誉等级(0.6)")
print("公司实力(0.4) = 盈利能力(0.15) + 稳定性(0.15) + 企业规模(0.05) + 公司市场竞争力(0.03) + 盈利预测可靠性(0.02)")
print("信誉等级(0.6) = 最终信用分数(0.25) + 经营风险(0.25) + 税负压力(0.1)")

# 计算各指标的最终权重
weights = {
    # 公司实力 0.4
    '盈利能力': 0.15,
    '稳定性': 0.15,
    '企业规模': 0.05,
    '公司市场竞争力': 0.03,
    '盈利预测可靠性': 0.02,
    
    # 信誉等级 0.6
    '最终信用分数': 0.25,
    '经营风险_反向': 0.25,  # 经营风险（已反向处理）
    '税负压力_反向': 0.1    # 税负压力（已反向处理）
}

print(f"\n各指标最终权重:")
total_weight = 0
for indicator, weight in weights.items():
    print(f"  {indicator}: {weight:.2f}")
    total_weight += weight

print(f"权重总和: {total_weight:.2f} (应该等于1.0)")

print(f"\n5. AHP综合评分计算")
print("="*50)

# 计算AHP综合得分
ahp_scores = np.zeros(len(standardized_df))

for indicator, weight in weights.items():
    if indicator in standardized_df.columns:
        ahp_scores += standardized_df[indicator].values * weight
    else:
        print(f"警告：未找到指标 {indicator}")

# 将AHP得分添加到数据框
standardized_df['AHP综合得分'] = ahp_scores

print(f"AHP综合得分统计:")
print(f"最高分: {ahp_scores.max():.4f}")
print(f"最低分: {ahp_scores.min():.4f}")
print(f"平均分: {ahp_scores.mean():.4f}")
print(f"标准差: {ahp_scores.std():.4f}")

print(f"\n6. 基于AHP的信贷分配策略")
print("="*50)

# 计算分配比例
def calculate_ahp_allocation(ahp_scores, min_threshold=0.001):
    """基于AHP得分计算分配比例"""
    # 设置最低分配门槛
    adjusted_scores = np.maximum(ahp_scores, min_threshold)
    
    # 基础分配比例
    base_allocation = adjusted_scores / adjusted_scores.sum()
    
    # 风险分级（基于AHP得分）
    risk_levels = np.where(ahp_scores < np.percentile(ahp_scores, 10), '极高风险',
                  np.where(ahp_scores < np.percentile(ahp_scores, 30), '高风险',
                  np.where(ahp_scores < np.percentile(ahp_scores, 70), '中等风险', '低风险')))
    
    # 风险调整系数
    risk_adjustment = np.where(risk_levels == '极高风险', 0.1,
                      np.where(risk_levels == '高风险', 0.5,
                      np.where(risk_levels == '中等风险', 0.8, 1.0)))
    
    # 最终分配比例
    final_allocation = base_allocation * risk_adjustment
    final_allocation = final_allocation / final_allocation.sum()  # 重新归一化
    
    return final_allocation, base_allocation, risk_adjustment, risk_levels

# 计算分配结果
final_allocation, base_allocation, risk_adjustment, risk_levels = calculate_ahp_allocation(ahp_scores)

# 创建最终结果数据框
ahp_strategy = pd.DataFrame({
    '企业代号': standardized_df['企业代号'],
    'AHP综合得分': ahp_scores,
    'AHP排名': np.argsort(-ahp_scores) + 1,  # 降序排名
    '基础分配比例(%)': base_allocation * 100,
    '风险调整系数': risk_adjustment,
    '最终分配比例(%)': final_allocation * 100,
    '风险等级': risk_levels
})

# 添加各指标标准化得分
for indicator in weights.keys():
    if indicator in standardized_df.columns:
        ahp_strategy[f'{indicator}_标准化'] = standardized_df[indicator]

# 按分配比例排序
ahp_strategy_sorted = ahp_strategy.sort_values('最终分配比例(%)', ascending=False)

print(f"前30名企业AHP信贷分配策略:")
print("="*100)
display_columns = ['企业代号', 'AHP综合得分', 'AHP排名', '最终分配比例(%)', '风险等级']
print(ahp_strategy_sorted[display_columns].head(30).round(4).to_string(index=False))

print(f"\n各风险等级分配汇总:")
print("="*50)
risk_summary = ahp_strategy.groupby('风险等级').agg({
    '企业代号': 'count',
    '最终分配比例(%)': ['sum', 'mean', 'min', 'max']
}).round(4)
risk_summary.columns = ['企业数量', '总分配比例(%)', '平均分配(%)', '最低分配(%)', '最高分配(%)']
print(risk_summary)

print(f"\n分配比例验证: {final_allocation.sum():.6f} (应该等于1.0)")

print(f"\n7. 详细分配结果")
print("="*50)

print("前20名企业详细分配:")
print("="*70)
for i, (idx, row) in enumerate(ahp_strategy_sorted.head(20).iterrows(), 1):
    print(f"{i:>2}. {row['企业代号']}: {row['最终分配比例(%)']:>7.4f}% (AHP得分: {row['AHP综合得分']:.4f}, {row['风险等级']})")

print(f"\n各风险等级具体分配:")
print("="*40)
for risk_level in ['低风险', '中等风险', '高风险', '极高风险']:
    if risk_level in risk_summary.index:
        row = risk_summary.loc[risk_level]
        print(f"{risk_level:>6}: {row['总分配比例(%)']:>7.2f}% ({row['企业数量']}家企业, 平均{row['平均分配(%)']:.4f}%)")

print(f"\n8. 保存AHP策略结果")
print("="*50)

# 保存详细结果
ahp_strategy_sorted.round(6).to_csv('AHP层次分析法信贷分配策略.csv', index=False, encoding='utf-8-sig')
print("✅ AHP策略已保存至: AHP层次分析法信贷分配策略.csv")

# 保存标准化指标数据
standardized_export = standardized_df.round(6)
standardized_export.to_csv('AHP标准化指标数据.csv', index=False, encoding='utf-8-sig')
print("✅ AHP标准化数据已保存至: AHP标准化指标数据.csv")

print(f"\n9. AHP策略总结")
print("="*50)
print(f"""
🎯 基于层次分析法(AHP)的信贷分配策略总结:

📊 权重体系:
- 公司实力(0.4): 盈利能力15% + 稳定性15% + 企业规模5% + 市场竞争力3% + 盈利可靠性2%
- 信誉等级(0.6): 信用分数25% + 经营风险25% + 税负压力10%

📈 评估结果:
- 参与企业: 123家
- 最高分企业: {ahp_strategy_sorted.iloc[0]['企业代号']} (AHP得分: {ahp_strategy_sorted.iloc[0]['AHP综合得分']:.4f})
- 最高分配比例: {ahp_strategy_sorted.iloc[0]['最终分配比例(%)']:.4f}%

💰 分配特点:
- 低风险企业: {risk_summary.loc['低风险', '企业数量']}家, 占比{risk_summary.loc['低风险', '总分配比例(%)']:.1f}%
- 中等风险企业: {risk_summary.loc['中等风险', '企业数量']}家, 占比{risk_summary.loc['中等风险', '总分配比例(%)']:.1f}%
- 高风险企业: {risk_summary.loc['高风险', '企业数量']}家, 占比{risk_summary.loc['高风险', '总分配比例(%)']:.1f}%
- 极高风险企业: {risk_summary.loc['极高风险', '企业数量']}家, 占比{risk_summary.loc['极高风险', '总分配比例(%)']:.2f}%

✅ AHP方法优势:
1. 权重设置符合业务逻辑和监管要求
2. 信誉等级权重更高，体现审慎原则
3. 多维度评估，涵盖财务和税务风险
4. 仅使用有效发票，确保数据准确性
""")

print("="*80)
print("AHP层次分析法信贷分配策略制定完成！")
print("="*80)
