"""
步骤2（最终修正版）：使用T1标准化数据进行相似度匹配
"""
import pandas as pd
import numpy as np
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics.pairwise import euclidean_distances, cosine_similarity

print("="*80)
print("步骤2：基于T1标准化数据的相似度匹配（最终修正版）")
print("="*80)

# 读取数据
t2_indicators = pd.read_csv('T2企业_七项指标.csv')
t1_standardized = pd.read_csv('../T1/T1完整标准化数据_含评级_包含ABCD级.csv')

print(f"T2企业数量: {len(t2_indicators)}家")
print(f"T1完整标准化数据: {len(t1_standardized)}家")

# T1数据已经包含评级信息，直接使用
print(f"T1完整数据: {len(t1_standardized)}家")
print(f"T1完整信誉评级分布（A/B/C/D）: {t1_standardized['信誉评级'].value_counts().to_dict()}")

# 用于匹配的7项指标（排除负债水平）
matching_indicators = ['盈利能力', '现金流稳定性', '企业规模', '税负压力', 
                      '公司市场竞争力', '盈利预测可靠性', '经营风险']

print(f"用于匹配的7项指标: {matching_indicators}")

# 提取T1和T2的指标数据
t1_features = t1_standardized[matching_indicators].values  # T1已经是标准化的
t2_features = t2_indicators[matching_indicators].values

print(f"T1特征矩阵形状: {t1_features.shape}")
print(f"T2特征矩阵形状: {t2_features.shape}")

# 数据清理
def clean_data(data):
    """清理异常值"""
    data = np.nan_to_num(data, nan=0.0, posinf=1, neginf=0)
    return data

t1_features_clean = clean_data(t1_features)
t2_features_clean = clean_data(t2_features)

# 对T2数据进行0-1标准化（与T1保持一致）
scaler = MinMaxScaler()
t2_normalized = scaler.fit_transform(t2_features_clean)

print("✅ 数据标准化完成")
print(f"T1数据: 已使用标准化数据集（0-1标准化）")
print(f"T2数据: 使用MinMaxScaler进行0-1标准化")
print(f"T1特征范围: [{t1_features_clean.min():.4f}, {t1_features_clean.max():.4f}]")
print(f"T2标准化后范围: [{t2_normalized.min():.4f}, {t2_normalized.max():.4f}]")

def perform_similarity_matching(t2_normalized, t1_features_clean, t1_data):
    """执行相似度匹配"""
    print("  正在计算相似度矩阵...")
    
    # 计算欧几里得距离和余弦相似度
    distances = euclidean_distances(t2_normalized, t1_features_clean)
    cosine_sim = cosine_similarity(t2_normalized, t1_features_clean)
    
    # 将距离转换为相似度（只使用欧氏距离）
    max_dist = np.max(distances)
    combined_scores = 1 - (distances / max_dist)
    
    # 找到每个T2企业最相似的T1企业
    most_similar_indices = np.argmax(combined_scores, axis=1)
    similarity_scores = np.max(combined_scores, axis=1)
    
    # 构建匹配结果
    matches = []
    for i, t1_idx in enumerate(most_similar_indices):
        t1_enterprise = t1_data.iloc[t1_idx]
        matches.append({
            'T2企业代号': t2_indicators.iloc[i]['企业代号'],
            'T1最相似企业': t1_enterprise['企业代号'],
            '继承信誉评级': t1_enterprise['信誉评级'],
            '相似度得分': similarity_scores[i],
            '欧式距离': distances[i, t1_idx],
            '余弦相似度': cosine_sim[i, t1_idx]
        })
    
    return pd.DataFrame(matches)

# 执行匹配
print("\n🔍 执行相似度匹配...")
similarity_results = perform_similarity_matching(t2_normalized, t1_features_clean, t1_standardized)

print(f"✅ 相似度匹配完成!")

# 统计继承的信誉评级分布（包含D级）
inherited_ratings = similarity_results['继承信誉评级'].value_counts()
print(f"\n📊 T2企业继承的信誉评级分布（包含D级）:")
total_t2 = len(similarity_results)
for rating in ['A', 'B', 'C', 'D']:
    count = inherited_ratings.get(rating, 0)
    percentage = count/total_t2*100
    print(f"  {rating}级: {count:>3d}家 ({percentage:5.1f}%)")

# 筛选出可投放信贷的企业（排除D级）
eligible_enterprises = similarity_results[similarity_results['继承信誉评级'] != 'D']
excluded_enterprises = similarity_results[similarity_results['继承信誉评级'] == 'D']

print(f"\n🎯 信贷投放资格筛选:")
print(f"  可投放信贷企业: {len(eligible_enterprises)}家 (排除D级)")
print(f"  排除的D级企业: {len(excluded_enterprises)}家")

print(f"\n📊 可投放信贷企业的评级分布:")
eligible_ratings = eligible_enterprises['继承信誉评级'].value_counts()
eligible_total = len(eligible_enterprises)
for rating in ['A', 'B', 'C']:
    count = eligible_ratings.get(rating, 0)
    percentage = count/eligible_total*100 if eligible_total > 0 else 0
    print(f"  {rating}级: {count:>3d}家 ({percentage:5.1f}%)")

# 相似度质量统计
print(f"\n📈 相似度匹配质量:")
print(f"  平均相似度: {similarity_results['相似度得分'].mean():.4f}")
print(f"  最高相似度: {similarity_results['相似度得分'].max():.4f}")
print(f"  最低相似度: {similarity_results['相似度得分'].min():.4f}")
print(f"  高质量匹配(>0.8): {(similarity_results['相似度得分'] > 0.8).sum()}家")
print(f"  中等质量匹配(0.6-0.8): {((similarity_results['相似度得分'] > 0.6) & (similarity_results['相似度得分'] <= 0.8)).sum()}家")
print(f"  低质量匹配(<0.6): {(similarity_results['相似度得分'] < 0.6).sum()}家")

# 合并T2企业完整数据
t2_with_ratings = pd.merge(t2_indicators, 
                          similarity_results[['T2企业代号', 'T1最相似企业', '继承信誉评级', '相似度得分']], 
                          left_on='企业代号', right_on='T2企业代号', how='left')
t2_with_ratings.drop('T2企业代号', axis=1, inplace=True)

print(f"\n🔝 前20家T2企业的匹配结果:")
print("企业代号 | 继承评级 | 相似度 | T1匹配企业 | 盈利能力 | 现金流稳定性 | 企业规模")
print("-" * 85)
for _, row in t2_with_ratings.head(20).iterrows():
    print(f"{row['企业代号']:>6s} | {row['继承信誉评级']:>6s} | {row['相似度得分']:>6.3f} | "
          f"{row['T1最相似企业']:>8s} | {row['盈利能力']:>8.0f} | {row['现金流稳定性']:>10.3f} | {row['企业规模']:>8.0f}")

# 分析各评级的匹配质量（包含D级）
print(f"\n📊 各评级匹配质量分析:")
for rating in ['A', 'B', 'C', 'D']:
    rating_data = similarity_results[similarity_results['继承信誉评级'] == rating]
    if len(rating_data) > 0:
        avg_sim = rating_data['相似度得分'].mean()
        count = len(rating_data)
        print(f"  {rating}级企业: {count}家，平均相似度: {avg_sim:.4f}")

# 最常被匹配的T1企业分析
print(f"\n🎯 最常被匹配的T1企业（前10名）:")
most_matched = similarity_results['T1最相似企业'].value_counts().head(10)
for t1_enterprise, count in most_matched.items():
    # 获取该T1企业的信誉评级
    t1_rating = t1_standardized[t1_standardized['企业代号'] == t1_enterprise]['信誉评级'].values[0]
    print(f"   {t1_enterprise}（{t1_rating}级）: 被匹配{count}次")

# 保存结果
similarity_results.to_csv('T2企业_最终相似度匹配结果.csv', index=False, encoding='utf-8-sig')
t2_with_ratings.to_csv('T2企业_最终完整数据.csv', index=False, encoding='utf-8-sig')

# 单独保存可投放信贷的企业数据
eligible_t2_complete = t2_with_ratings[t2_with_ratings['继承信誉评级'] != 'D']
eligible_t2_complete.to_csv('T2企业_可投放信贷_完整数据.csv', index=False, encoding='utf-8-sig')

print(f"\n✅ 匹配结果已保存:")
print("- T2企业_最终相似度匹配结果.csv (所有302家企业)")
print("- T2企业_最终完整数据.csv (所有302家企业)")
print(f"- T2企业_可投放信贷_完整数据.csv ({len(eligible_t2_complete)}家A/B/C级企业)")

print(f"\n🎉 相似度匹配分析完成！")
print(f"现在可以基于真实的相似度匹配结果进行多目标优化建模。")
