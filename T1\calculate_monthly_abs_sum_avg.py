import pandas as pd
import numpy as np
from datetime import datetime

# 读取数据
print("正在读取数据...")
# 进项发票数据 (2.csv)
inbound_df = pd.read_csv('2.csv')
# 销项发票数据 (3.csv)  
outbound_df = pd.read_csv('3.csv')

print(f"进项发票数据行数: {len(inbound_df)}")
print(f"销项发票数据行数: {len(outbound_df)}")

# 数据预处理
def preprocess_data(df, invoice_type):
    # 转换日期格式
    df['开票日期'] = pd.to_datetime(df['开票日期'])
    
    # 提取年月
    df['年月'] = df['开票日期'].dt.to_period('M')
    
    # 只保留有效发票
    df_valid = df[df['发票状态'] == '有效发票'].copy()
    
    print(f"{invoice_type}有效发票数据行数: {len(df_valid)}")
    
    return df_valid

# 预处理数据
inbound_valid = preprocess_data(inbound_df, "进项")
outbound_valid = preprocess_data(outbound_df, "销项")

# 按企业和月份汇总进项发票（取绝对值）
print("正在汇总进项发票...")
inbound_monthly = inbound_valid.groupby(['企业代号', '年月'])['价税合计'].apply(lambda x: abs(x).sum()).reset_index()
inbound_monthly.rename(columns={'价税合计': '进项价税合计绝对值'}, inplace=True)

# 按企业和月份汇总销项发票（取绝对值）
print("正在汇总销项发票...")
outbound_monthly = outbound_valid.groupby(['企业代号', '年月'])['价税合计'].apply(lambda x: abs(x).sum()).reset_index()
outbound_monthly.rename(columns={'价税合计': '销项价税合计绝对值'}, inplace=True)

# 合并进项和销项数据
print("正在合并数据...")
monthly_combined = pd.merge(inbound_monthly, outbound_monthly, on=['企业代号', '年月'], how='outer')

# 填充缺失值为0
monthly_combined['进项价税合计绝对值'] = monthly_combined['进项价税合计绝对值'].fillna(0)
monthly_combined['销项价税合计绝对值'] = monthly_combined['销项价税合计绝对值'].fillna(0)

# 计算每月绝对值之和
monthly_combined['月度绝对值之和'] = monthly_combined['进项价税合计绝对值'] + monthly_combined['销项价税合计绝对值']

print("月度汇总数据示例:")
print(monthly_combined.head(10))

# 计算每个企业每月绝对值之和的平均值
print("正在计算每个企业的平均值...")
company_avg = monthly_combined.groupby('企业代号')['月度绝对值之和'].mean().reset_index()
company_avg.rename(columns={'月度绝对值之和': '月度绝对值之和平均值'}, inplace=True)

# 计算额外的统计信息
print("正在计算统计信息...")
stats = monthly_combined.groupby('企业代号').agg({
    '月度绝对值之和': ['count', 'mean', 'median', 'std', 'min', 'max'],
    '进项价税合计绝对值': 'mean',
    '销项价税合计绝对值': 'mean'
}).reset_index()

# 扁平化列名
stats.columns = ['企业代号', '月份数量', '月度绝对值之和平均值', '月度绝对值之和中位数', 
                '月度绝对值之和标准差', '月度绝对值之和最小值', '月度绝对值之和最大值',
                '进项平均值', '销项平均值']

print(f"\n计算完成！共有 {len(stats)} 家企业的数据")
print("\n结果示例:")
print(stats.head(10))

# 保存结果
stats.to_csv('企业月度绝对值之和平均值结果.csv', index=False, encoding='utf-8-sig')
print(f"\n结果已保存到: 企业月度绝对值之和平均值结果.csv")

# 显示一些关键统计信息
print(f"\n=== 整体统计信息 ===")
print(f"平均值的均值: {stats['月度绝对值之和平均值'].mean():.2f}")
print(f"平均值的标准差: {stats['月度绝对值之和平均值'].std():.2f}")
print(f"平均值的最小值: {stats['月度绝对值之和平均值'].min():.2f}")
print(f"平均值的最大值: {stats['月度绝对值之和平均值'].max():.2f}")

# 按交易活跃度分类
print(f"\n=== 企业活跃度分布 ===")
high_activity = stats['月度绝对值之和平均值'] > stats['月度绝对值之和平均值'].quantile(0.75)
medium_activity = (stats['月度绝对值之和平均值'] > stats['月度绝对值之和平均值'].quantile(0.25)) & (stats['月度绝对值之和平均值'] <= stats['月度绝对值之和平均值'].quantile(0.75))
low_activity = stats['月度绝对值之和平均值'] <= stats['月度绝对值之和平均值'].quantile(0.25)

print(f"高活跃度企业 (前25%): {high_activity.sum()}家")
print(f"中等活跃度企业 (25%-75%): {medium_activity.sum()}家") 
print(f"低活跃度企业 (后25%): {low_activity.sum()}家")

# 显示前10名最活跃的企业
print(f"\n=== 前10名最活跃企业 ===")
top10 = stats.nlargest(10, '月度绝对值之和平均值')[['企业代号', '月度绝对值之和平均值']]
for idx, row in top10.iterrows():
    print(f"{row['企业代号']}: {row['月度绝对值之和平均值']:,.2f}元")
