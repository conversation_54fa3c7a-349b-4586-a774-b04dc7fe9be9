"""
计算T2企业七项指标的0-1标准化数据
"""
import pandas as pd
import numpy as np
from sklearn.preprocessing import MinMaxScaler

print("="*60)
print("T2企业七项指标0-1标准化处理")
print("="*60)

# 读取T2企业七项指标数据
df = pd.read_csv('T2企业_七项指标.csv')
print(f"原始数据形状: {df.shape}")
print(f"企业数量: {len(df)}家")

# 提取七项指标列
indicators = ['盈利能力', '现金流稳定性', '企业规模', '税负压力', 
              '公司市场竞争力', '盈利预测可靠性', '经营风险']

print(f"\n七项指标: {indicators}")

# 提取指标数据
indicator_data = df[indicators].values
print(f"指标数据形状: {indicator_data.shape}")

# 数据清理（处理异常值和无穷值）
def clean_data(data):
    """清理数据中的异常值和无穷值"""
    data = np.nan_to_num(data, nan=0.0, posinf=1, neginf=0)
    return data

indicator_data_clean = clean_data(indicator_data)

print(f"\n原始数据统计:")
for i, indicator in enumerate(indicators):
    col_data = indicator_data_clean[:, i]
    print(f"  {indicator}: min={col_data.min():.4f}, max={col_data.max():.4f}, mean={col_data.mean():.4f}")

# 0-1标准化
scaler = MinMaxScaler()
normalized_data = scaler.fit_transform(indicator_data_clean)

print(f"\n标准化后数据统计:")
for i, indicator in enumerate(indicators):
    col_data = normalized_data[:, i]
    print(f"  {indicator}: min={col_data.min():.4f}, max={col_data.max():.4f}, mean={col_data.mean():.4f}")

# 创建标准化后的DataFrame
normalized_df = pd.DataFrame(normalized_data, columns=indicators)
normalized_df.insert(0, '企业代号', df['企业代号'])

print(f"\n标准化后数据形状: {normalized_df.shape}")

# 显示前20行数据
print(f"\n前20家企业的标准化指标:")
print("企业代号 |   盈利能力 | 现金流稳定性 |   企业规模 |   税负压力 | 市场竞争力 | 盈利可靠性 |   经营风险")
print("-" * 100)
for _, row in normalized_df.head(20).iterrows():
    print(f"{row['企业代号']:>6s} | {row['盈利能力']:>9.4f} | {row['现金流稳定性']:>10.4f} | "
          f"{row['企业规模']:>9.4f} | {row['税负压力']:>9.4f} | {row['公司市场竞争力']:>9.4f} | "
          f"{row['盈利预测可靠性']:>9.4f} | {row['经营风险']:>9.4f}")

# 保存标准化后的数据
output_file = 'T2企业_七项指标_标准化.csv'
normalized_df.to_csv(output_file, index=False, encoding='utf-8-sig')

print(f"\n✅ 标准化数据已保存到: {output_file}")

# 显示一些统计信息
print(f"\n📊 标准化质量检查:")
print(f"  所有数据都在[0,1]范围内: {(normalized_data >= 0).all() and (normalized_data <= 1).all()}")
print(f"  数据中无NaN值: {not np.isnan(normalized_data).any()}")
print(f"  数据中无无穷值: {not np.isinf(normalized_data).any()}")

# 显示标准化参数
print(f"\n🔧 标准化参数(min值):")
for i, indicator in enumerate(indicators):
    print(f"  {indicator}: {scaler.data_min_[i]:.4f}")

print(f"\n🔧 标准化参数(max值):")
for i, indicator in enumerate(indicators):
    print(f"  {indicator}: {scaler.data_max_[i]:.4f}")

print(f"\n🎉 T2企业七项指标0-1标准化完成！")
