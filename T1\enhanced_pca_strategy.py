"""
计算新增税务指标并重新进行PCA分析制定信贷分配策略
新增指标：税负压力、公司市场竞争力、盈利预测可靠性、经营风险
"""
import pandas as pd
import numpy as np
from sklearn.preprocessing import MinMaxScaler
from sklearn.decomposition import PCA
from scipy.stats import rankdata
import warnings
warnings.filterwarnings('ignore')

print("="*80)
print("基于增强指标的PCA信贷分配策略")
print("="*80)

# 读取原始数据
data_enterprise = pd.read_csv('企业四项指标原始数据.csv')
data_input = pd.read_csv('2.csv')  # 进项发票数据
data_output = pd.read_csv('3.csv')  # 销项发票数据

print("1. 计算新增税务指标")
print("="*50)

# 为每个企业计算税务指标
def calculate_tax_indicators():
    """计算税务相关指标"""
    tax_indicators = {}
    
    for company in data_enterprise['企业代号']:
        # 获取该企业的进项和销项数据，只考虑有效发票
        input_data = data_input[(data_input['企业代号'] == company) & (data_input['发票状态'] == '有效发票')]
        output_data = data_output[(data_output['企业代号'] == company) & (data_output['发票状态'] == '有效发票')]
        
        if len(input_data) == 0 or len(output_data) == 0:
            # 如果没有数据，设为0
            tax_indicators[company] = {
                '税负压力': 0,
                '公司市场竞争力': 0,
                '盈利预测可靠性': 0,
                '经营风险': 0
            }
            continue
        
        # 计算进项税率和销项税率
        # 税率 = 税额 / 金额
        input_tax_rate = input_data['税额'].sum() / input_data['金额'].sum() if input_data['金额'].sum() > 0 else 0
        output_tax_rate = output_data['税额'].sum() / output_data['金额'].sum() if output_data['金额'].sum() > 0 else 0
        
        # 1. 税负压力：销项税率 - 进项税率，差值越大压力越大
        tax_pressure = output_tax_rate - input_tax_rate
        
        # 2. 公司市场竞争力：销项税总额（销项税越高，市场竞争力越强）
        market_competitiveness = output_data['税额'].sum()
        
        # 3. 盈利预测可靠性：每月销项税的稳定性（用变异系数衡量，越小越稳定）
        # 先转换日期格式，只用有效发票数据
        output_data_copy = output_data.copy()
        output_data_copy['开票日期'] = pd.to_datetime(output_data_copy['开票日期'])
        output_data_copy['年月'] = output_data_copy['开票日期'].dt.to_period('M')
        
        monthly_output_tax = output_data_copy.groupby('年月')['税额'].sum()
        if len(monthly_output_tax) > 1:
            cv = monthly_output_tax.std() / monthly_output_tax.mean() if monthly_output_tax.mean() > 0 else float('inf')
            profit_reliability = 1 / (1 + cv)  # 转换为正向指标，越大越稳定
        else:
            profit_reliability = 0.5  # 如果只有一个月的数据，给中等值
        
        # 4. 经营风险：销项税率 - 进项税率（与税负压力相同，但从风险角度解释）
        operating_risk = output_tax_rate - input_tax_rate
        
        tax_indicators[company] = {
            '税负压力': tax_pressure,
            '公司市场竞争力': market_competitiveness,
            '盈利预测可靠性': profit_reliability,
            '经营风险': operating_risk
        }
        
        print(f"{company}: 进项税率={input_tax_rate:.4f}, 销项税率={output_tax_rate:.4f}")
    
    return tax_indicators

# 计算税务指标
print("正在计算各企业税务指标...")
tax_indicators = calculate_tax_indicators()

# 转换为DataFrame
tax_df = pd.DataFrame.from_dict(tax_indicators, orient='index')
tax_df.reset_index(inplace=True)
tax_df.rename(columns={'index': '企业代号'}, inplace=True)

print(f"\n税务指标计算完成，前10家企业结果：")
print(tax_df.head(10).round(4))

print(f"\n税务指标统计描述：")
print(tax_df[['税负压力', '公司市场竞争力', '盈利预测可靠性', '经营风险']].describe().round(4))

print(f"\n2. 合并所有指标数据")
print("="*50)

# 合并原有指标和新增指标
merged_data = pd.merge(data_enterprise, tax_df, on='企业代号', how='left')

# 填充可能的缺失值
merged_data = merged_data.fillna(0)

print(f"合并后数据维度: {merged_data.shape}")
print(f"所有指标列: {list(merged_data.columns)}")

# 选择用于分析的指标
indicators = ['最终信用分数', '盈利能力', '企业规模', '稳定性', 
              '税负压力', '公司市场竞争力', '盈利预测可靠性', '经营风险']

# 处理经营风险指标（风险越高越不好，需要反向处理）
merged_data['经营风险_反向'] = -merged_data['经营风险']  # 反向处理
indicators_for_analysis = ['最终信用分数', '盈利能力', '企业规模', '稳定性', 
                          '税负压力', '公司市场竞争力', '盈利预测可靠性', '经营风险_反向']

# 注意：税负压力也是负向指标，需要反向处理
merged_data['税负压力_反向'] = -merged_data['税负压力']
indicators_for_analysis = ['最终信用分数', '盈利能力', '企业规模', '稳定性', 
                          '税负压力_反向', '公司市场竞争力', '盈利预测可靠性', '经营风险_反向']

analysis_data = merged_data[['企业代号'] + indicators_for_analysis].copy()
print(f"\n用于分析的数据（前5行）：")
print(analysis_data.head())

print(f"\n3. 数据标准化处理")
print("="*50)

# 提取数值数据进行标准化
raw_data = analysis_data[indicators_for_analysis].values
companies = analysis_data['企业代号'].values

# 0-1标准化
scaler = MinMaxScaler()
standardized_data = scaler.fit_transform(raw_data)

# 创建标准化后的数据框
standardized_df = pd.DataFrame(
    standardized_data, 
    columns=[f'{col}_标准化' for col in indicators_for_analysis],
    index=companies
)

print("标准化后的数据统计:")
print(standardized_df.describe().round(4))

print(f"\n4. PCA主成分分析")
print("="*50)

# PCA分析
pca = PCA()
pca_result = pca.fit_transform(standardized_data)

print(f"各主成分解释方差比:")
for i, ratio in enumerate(pca.explained_variance_ratio_):
    print(f"  第{i+1}主成分: {ratio:.4f}")

print(f"前3个主成分累计解释方差: {sum(pca.explained_variance_ratio_[:3]):.4f}")

# 分析主成分载荷
print(f"\n前3个主成分的载荷分析:")
components_df = pd.DataFrame(
    pca.components_[:3].T,
    columns=['第1主成分', '第2主成分', '第3主成分'],
    index=indicators_for_analysis
)
print(components_df.round(4))

print(f"\n5. 基于PCA的信贷分配策略")
print("="*50)

# 使用前3个主成分计算综合得分
pca_scores_weighted = np.dot(pca_result[:, :3], pca.explained_variance_ratio_[:3])

# 标准化到0-1
pca_scores_normalized = (pca_scores_weighted - pca_scores_weighted.min()) / (pca_scores_weighted.max() - pca_scores_weighted.min())

# 计算信贷分配比例
def calculate_enhanced_credit_allocation(pca_scores, companies, min_threshold=0.001):
    """计算增强版信贷分配比例"""
    # 对于分数为0或接近0的企业，设置最低门槛
    adjusted_scores = np.maximum(pca_scores, min_threshold)
    
    # 计算基础分配比例
    base_allocation = adjusted_scores / adjusted_scores.sum()
    
    # 风险调整：根据PCA得分分级调整
    risk_adjustment = np.where(pca_scores < 0.1, 0.1,  # 极高风险
                      np.where(pca_scores < 0.3, 0.5,  # 高风险  
                      np.where(pca_scores < 0.6, 0.8,  # 中等风险
                              1.0)))                    # 低风险
    
    adjusted_allocation = base_allocation * risk_adjustment
    final_allocation = adjusted_allocation / adjusted_allocation.sum()
    
    return final_allocation, base_allocation, risk_adjustment

# 计算分配比例
final_allocation, base_allocation, risk_adjustment = calculate_enhanced_credit_allocation(
    pca_scores_normalized, companies
)

# 创建最终结果
enhanced_strategy = pd.DataFrame({
    '企业代号': companies,
    **{f'{ind}_标准化': standardized_data[:, i] for i, ind in enumerate(indicators_for_analysis)},
    'PCA综合得分': pca_scores_normalized,
    'PCA排名': len(companies) + 1 - rankdata(pca_scores_normalized, method='min'),
    '基础分配比例(%)': base_allocation * 100,
    '风险调整系数': risk_adjustment,
    '最终分配比例(%)': final_allocation * 100,
    '风险等级': np.where(pca_scores_normalized < 0.1, '极高风险',
                      np.where(pca_scores_normalized < 0.3, '高风险',
                      np.where(pca_scores_normalized < 0.6, '中等风险', '低风险')))
})

# 按分配比例排序
enhanced_strategy_sorted = enhanced_strategy.sort_values('最终分配比例(%)', ascending=False)

print(f"前30名企业增强版信贷分配策略:")
print("="*120)
display_columns = ['企业代号', 'PCA综合得分', 'PCA排名', '最终分配比例(%)', '风险等级']
print(enhanced_strategy_sorted[display_columns].head(30).round(4).to_string(index=False))

print(f"\n各风险等级汇总:")
risk_summary = enhanced_strategy.groupby('风险等级').agg({
    '企业代号': 'count',
    '最终分配比例(%)': ['sum', 'mean']
}).round(4)
print(risk_summary)

print(f"\n6. 新旧策略对比分析")
print("="*50)

# 读取原策略结果进行对比（如果文件存在）
try:
    old_strategy = pd.read_csv('标准化后综合评估结果.csv', encoding='utf-8')
    
    # 合并对比
    comparison = pd.merge(
        enhanced_strategy_sorted[['企业代号', 'PCA综合得分', '最终分配比例(%)', '风险等级']],
        old_strategy[['企业代号', 'PCA得分', '综合平均得分']],
        on='企业代号',
        suffixes=('_新', '_旧')
    )
    
    print(f"策略对比（前15名企业）:")
    print(comparison[['企业代号', 'PCA综合得分_新', 'PCA得分_旧', '最终分配比例(%)', '风险等级_新']].head(15).round(4).to_string(index=False))
    
    # 排名变化分析
    old_ranking = {row['企业代号']: idx+1 for idx, row in old_strategy.sort_values('综合平均得分', ascending=False).iterrows()}
    new_ranking = {row['企业代号']: idx+1 for idx, row in enhanced_strategy_sorted.iterrows()}
    
    ranking_changes = []
    for company in companies:
        old_rank = old_ranking.get(company, len(companies))
        new_rank = new_ranking.get(company, len(companies))
        change = old_rank - new_rank  # 正数表示排名上升
        ranking_changes.append({
            '企业代号': company,
            '原排名': old_rank,
            '新排名': new_rank,
            '排名变化': change
        })
    
    ranking_df = pd.DataFrame(ranking_changes).sort_values('排名变化', ascending=False)
    
    print(f"\n排名变化最大的10家企业（正数表示排名上升）:")
    print(ranking_df.head(10)[['企业代号', '原排名', '新排名', '排名变化']].to_string(index=False))
    
    print(f"\n排名下降最大的10家企业:")
    print(ranking_df.tail(10)[['企业代号', '原排名', '新排名', '排名变化']].to_string(index=False))
    
except FileNotFoundError:
    print("未找到原策略文件，跳过对比分析")

print(f"\n7. 保存增强版策略结果")
print("="*50)

# 保存详细结果
enhanced_strategy_sorted.round(6).to_csv('增强版PCA信贷分配策略.csv', index=False, encoding='utf-8-sig')
print("✅ 增强版策略已保存至: 增强版PCA信贷分配策略.csv")

# 保存税务指标数据
tax_df.round(6).to_csv('企业税务指标数据.csv', index=False, encoding='utf-8-sig')
print("✅ 税务指标已保存至: 企业税务指标数据.csv")

# 保存标准化数据
merged_standardized = pd.DataFrame({
    '企业代号': companies,
    **{ind: standardized_data[:, i] for i, ind in enumerate(indicators_for_analysis)}
})
merged_standardized.round(6).to_csv('增强版标准化指标数据.csv', index=False, encoding='utf-8-sig')
print("✅ 增强版标准化数据已保存至: 增强版标准化指标数据.csv")

print(f"\n8. 最终策略总结")
print("="*50)
print(f"""
🎯 增强版PCA信贷分配策略总结:

📊 指标体系升级:
- 原有4个指标 → 8个指标
- 新增税务维度：税负压力、市场竞争力、盈利可靠性、经营风险
- 更全面反映企业真实经营状况

📈 PCA分析结果:
- 前3主成分累计解释方差: {sum(pca.explained_variance_ratio_[:3]):.2%}
- 主要影响因子: {indicators_for_analysis[np.argmax(np.abs(pca.components_[0]))]}

💰 分配策略特点:
- 顶级企业: {enhanced_strategy_sorted.iloc[0]['企业代号']} (分配比例: {enhanced_strategy_sorted.iloc[0]['最终分配比例(%)']:.4f}%)
- 风险分级更精细：极高风险/高风险/中等风险/低风险
- 总分配验证: {final_allocation.sum():.6f} (应等于1.0)

✅ 策略优势:
1. 指标更全面，涵盖税务合规性
2. 风险识别更精准，分级更细致  
3. 分配更科学，考虑经营税务风险
4. 符合监管要求，体现审慎原则
""")

print(f"\n" + "="*80)
print("增强版PCA信贷分配策略制定完成！")
print("="*80)
