"""
最终修正AHP排名和风险等级逻辑
核心修正：得分高 → 排名高 → 风险低 → 分配多
"""
import pandas as pd
import numpy as np
from scipy.stats import rankdata
import warnings
warnings.filterwarnings('ignore')

print("="*80)
print("最终修正AHP排名和风险等级逻辑")
print("="*80)

# 读取原始数据
df = pd.read_csv('AHP层次分析法信贷分配策略.csv', encoding='gbk')

print("1. 修正AHP排名")
print("="*50)

# 计算正确的AHP排名（得分越高，排名越靠前，即数字越小）
scores = df['AHP综合得分'].values
correct_ranks = rankdata(-scores, method='min').astype(int)

df_corrected = df.copy()
df_corrected['AHP排名'] = correct_ranks

print(f"✅ 排名修正完成：{len(df)}家企业重新排名")

print("\n2. 修正风险等级逻辑")
print("="*50)
print("正确逻辑：AHP得分越高 → 排名越靠前 → 风险越低")

def get_correct_risk_level(rank, total):
    """
    基于正确排名分配风险等级
    排名越靠前(数字越小)，风险越低
    """
    if rank <= total * 0.3:  # 前30%排名靠前的企业
        return '低风险'
    elif rank <= total * 0.7:  # 30%-70%
        return '中等风险'
    elif rank <= total * 0.9:  # 70%-90%
        return '高风险'
    else:  # 后10%
        return '极高风险'

# 重新分配风险等级
n_companies = len(df_corrected)
df_corrected['风险等级'] = df_corrected['AHP排名'].apply(lambda x: get_correct_risk_level(x, n_companies))

print("风险等级分布:")
risk_dist = df_corrected['风险等级'].value_counts().sort_index()
for risk, count in risk_dist.items():
    pct = count / n_companies * 100
    print(f"{risk:>8}: {count:>3}家 ({pct:>5.1f}%)")

print("\n3. 重新计算分配策略")
print("="*50)

# 风险系数：风险越低，系数越高，分配越多
risk_coefficients = {
    '低风险': 1.0,      # 最高分配
    '中等风险': 0.8,    # 较高分配
    '高风险': 0.5,      # 较低分配
    '极高风险': 0.1     # 最低分配
}

# 计算基础分配（基于AHP得分）
base_scores = df_corrected['AHP综合得分'].values
base_allocation = base_scores / base_scores.sum()

# 应用风险调整
df_corrected['风险系数'] = df_corrected['风险等级'].map(risk_coefficients)
adjusted_allocation = base_allocation * df_corrected['风险系数'].values

# 重新归一化到100%
final_allocation = adjusted_allocation / adjusted_allocation.sum()

# 更新分配比例
df_corrected['基础分配比例(%)'] = base_allocation * 100
df_corrected['最终分配比例(%)'] = final_allocation * 100

print("✅ 分配策略重新计算完成")

print("\n4. 验证修正结果")
print("="*50)
print("前20名企业（得分最高，风险最低，分配最多）:")
print("排名 | 企业代号 | AHP得分   | 风险等级 | 分配比例(%)")
print("-"*55)

df_top20 = df_corrected.sort_values('AHP排名').head(20)
for _, row in df_top20.iterrows():
    print(f'{row["AHP排名"]:>3} | {row["企业代号"]:>6} | {row["AHP综合得分"]:>8.6f} | {row["风险等级"]:>6} | {row["最终分配比例(%)"]:>7.4f}')

print("\n5. 分配策略统计")
print("="*50)

# 按风险等级统计分配情况
risk_allocation = df_corrected.groupby('风险等级').agg({
    '企业代号': 'count',
    '最终分配比例(%)': ['sum', 'mean']
}).round(4)

print("各风险等级分配统计:")
print("风险等级 | 企业数 | 总分配比例(%) | 平均分配比例(%)")
print("-"*50)
for risk in ['低风险', '中等风险', '高风险', '极高风险']:
    if risk in risk_allocation.index:
        count = int(risk_allocation.loc[risk, ('企业代号', 'count')])
        total_alloc = risk_allocation.loc[risk, ('最终分配比例(%)', 'sum')]
        avg_alloc = risk_allocation.loc[risk, ('最终分配比例(%)', 'mean')]
        print(f'{risk:>6} | {count:>4} | {total_alloc:>11.4f} | {avg_alloc:>13.6f}')

print(f"\n总分配验证: {df_corrected['最终分配比例(%)'].sum():.6f}% (应为100%)")

print("\n6. 保存最终修正结果")
print("="*50)

# 整理输出字段
output_columns = [
    '企业代号', 'AHP综合得分', 'AHP排名', '基础分配比例(%)', 
    '风险等级', '风险系数', '最终分配比例(%)'
]

df_final = df_corrected[output_columns].sort_values('AHP排名')

# 保存最终修正版本
df_final.to_csv('AHP层次分析法信贷分配策略_最终修正版.csv', index=False, encoding='utf-8-sig')
print("✅ 最终修正版已保存至: AHP层次分析法信贷分配策略_最终修正版.csv")

print("\n7. 修正总结")
print("="*80)
print("🎯 关键修正:")
print("1. ✅ AHP排名：基于AHP综合得分正确计算")
print("2. ✅ 风险等级：排名越前风险越低（符合常理）")
print("3. ✅ 分配逻辑：得分高→排名前→风险低→分配多")
print()
print("📊 最终逻辑链条:")
print("AHP综合得分↗ → AHP排名↗（数字↘）→ 风险等级↘ → 分配比例↗")
print()
print(f"🏆 最优企业: {df_final.iloc[0]['企业代号']} (得分:{df_final.iloc[0]['AHP综合得分']:.6f}, 排名:1, {df_final.iloc[0]['风险等级']}, 分配:{df_final.iloc[0]['最终分配比例(%)']:.4f}%)")
print(f"⚠️ 最差企业: {df_final.iloc[-1]['企业代号']} (得分:{df_final.iloc[-1]['AHP综合得分']:.6f}, 排名:{df_final.iloc[-1]['AHP排名']}, {df_final.iloc[-1]['风险等级']}, 分配:{df_final.iloc[-1]['最终分配比例(%)']:.4f}%)")
print()
print("✅ 现在AHP排名与综合得分完全一致，逻辑正确！")
