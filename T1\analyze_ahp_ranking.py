"""
分析AHP综合得分和排名的关系问题
"""
import pandas as pd

# 读取数据
df = pd.read_csv('AHP层次分析法信贷分配策略.csv', encoding='gbk')

print('='*60)
print('AHP综合得分和排名关系分析')
print('='*60)
print(f'数据集中总共有{len(df)}家企业\n')

# 1. AHP综合得分最高的前10家企业
print('1. AHP综合得分最高的前10家企业:')
print('-'*50)
top_scores = df.nlargest(10, 'AHP综合得分')[['企业代号', 'AHP综合得分', 'AHP排名', '风险等级']]
print('排序 | 企业代号 | AHP综合得分 | AHP排名 | 风险等级')
print('-'*50)
for i, (idx, row) in enumerate(top_scores.iterrows()):
    print(f'{i+1:2}   | {row["企业代号"]:>6} | {row["AHP综合得分"]:>10.6f} | {row["AHP排名"]:>5} | {row["风险等级"]}')

print('\n2. AHP排名最靠前的前10家企业:')
print('-'*50)
top_ranks = df.nsmallest(10, 'AHP排名')[['企业代号', 'AHP综合得分', 'AHP排名', '风险等级']]
print('排名 | 企业代号 | AHP综合得分 | 风险等级')
print('-'*40)
for idx, row in top_ranks.iterrows():
    print(f'{row["AHP排名"]:2}   | {row["企业代号"]:>6} | {row["AHP综合得分"]:>10.6f} | {row["风险等级"]}')

print('\n3. 问题分析:')
print('='*50)
print('🚨 发现问题：AHP排名与AHP综合得分不匹配！')
print()
print('观察发现：')
print('• E42得分最高(0.857308)，但排名是35，不是1')
print('• E99排名第1，但得分只有0.520703，远低于E42')
print('• 这说明当前的"AHP排名"不是基于AHP综合得分计算的')

# 4. 计算正确的排名
print('\n4. 正确排名计算:')
print('='*50)
df_correct = df.copy()
df_correct['正确AHP排名'] = df_correct['AHP综合得分'].rank(method='min', ascending=False).astype(int)

print('对比分析（前15家企业）:')
print('企业代号 | AHP综合得分 | 原排名 | 正确排名 | 差异')
print('-'*55)
comparison = df_correct[['企业代号', 'AHP综合得分', 'AHP排名', '正确AHP排名']].head(15)
for _, row in comparison.iterrows():
    diff = abs(row['AHP排名'] - row['正确AHP排名'])
    print(f'{row["企业代号"]:>6} | {row["AHP综合得分"]:>10.6f} | {row["AHP排名"]:>4} | {row["正确AHP排名"]:>6} | {diff:>4}')

# 5. 查看原始排名的依据
print('\n5. 原始排名可能的依据分析:')
print('='*50)

# 检查原排名是否基于某个其他指标
columns_to_check = ['月利润中位数_标准化', '稳定性_标准化', '企业规模_标准化', 
                   '公司市场评价分_标准化', '月利润分配比例(%)', '风险系数']

for col in columns_to_check:
    if col in df.columns:
        correlation = df['AHP排名'].corr(df[col])
        print(f'{col:>20}: 与AHP排名的相关系数 = {correlation:.4f}')

print('\n6. 结论和建议:')
print('='*50)
print('📋 问题总结：')
print('1. 当前的"AHP排名"不是基于"AHP综合得分"排序得出的')
print('2. 这导致了"排名高，得分反而低"的矛盾现象')
print('3. 应该使用AHP综合得分进行重新排名')
print()
print('✅ 建议解决方案：')
print('1. 重新计算排名：基于AHP综合得分从高到低排序')
print('2. 更新所有相关文件中的排名字段')
print('3. 确保排名与得分的一致性')

# 保存正确排名的结果
df_corrected = df_correct.sort_values('正确AHP排名')[['企业代号', 'AHP综合得分', '正确AHP排名', '风险等级']]
df_corrected.to_csv('AHP正确排名结果.csv', index=False, encoding='utf-8-sig')
print('\n📁 已保存正确排名结果到: AHP正确排名结果.csv')
