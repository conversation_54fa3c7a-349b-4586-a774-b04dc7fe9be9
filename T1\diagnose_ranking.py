import pandas as pd
import numpy as np
from scipy.stats import rankdata

# 读取数据
df = pd.read_csv('AHP层次分析法信贷分配策略.csv', encoding='gbk')

scores = df['AHP综合得分'].values
current_ranks = df['AHP排名'].values

# 计算正确的排名
correct_ranks_argsort = np.argsort(-scores) + 1
correct_ranks_scipy = rankdata(-scores, method='min').astype(int)

print("排名方法对比分析")
print("="*60)
print("企业代号 | AHP得分   | 当前排名 | argsort+1 | scipy正确")
print("-"*60)

for i in range(15):
    print(f'{df.iloc[i]["企业代号"]:>6} | {scores[i]:>8.6f} | {current_ranks[i]:>6} | {correct_ranks_argsort[i]:>7} | {correct_ranks_scipy[i]:>8}')

print(f"\n差异统计:")
print(f"当前排名与argsort+1相同: {np.sum(current_ranks == correct_ranks_argsort)} / {len(current_ranks)}")
print(f"当前排名与scipy相同: {np.sum(current_ranks == correct_ranks_scipy)} / {len(current_ranks)}")

# 找出差异最大的案例
diff_argsort = np.abs(current_ranks - correct_ranks_argsort)
diff_scipy = np.abs(current_ranks - correct_ranks_scipy)

print(f"\n最大差异案例(argsort方法):")
max_diff_idx = np.argmax(diff_argsort)
print(f"企业 {df.iloc[max_diff_idx]['企业代号']}: 得分{scores[max_diff_idx]:.6f}, 当前排名{current_ranks[max_diff_idx]}, 应该是{correct_ranks_argsort[max_diff_idx]}, 差异{diff_argsort[max_diff_idx]}")

print(f"\n问题诊断:")
if np.array_equal(current_ranks, correct_ranks_argsort):
    print("✅ 当前排名与argsort+1计算方法一致")
elif np.array_equal(current_ranks, correct_ranks_scipy):
    print("✅ 当前排名与scipy.rankdata方法一致")  
else:
    print("❌ 当前排名与标准计算方法都不一致，存在计算错误")
    
    # 检查是否是数据读取的顺序问题
    df_sorted_by_score = df.sort_values('AHP综合得分', ascending=False).reset_index(drop=True)
    print(f"\n检查是否是排序问题:")
    print(f"按得分降序后的前5名:")
    for i in range(5):
        print(f"{i+1}. {df_sorted_by_score.iloc[i]['企业代号']}: 得分{df_sorted_by_score.iloc[i]['AHP综合得分']:.6f}, 原排名{df_sorted_by_score.iloc[i]['AHP排名']}")
