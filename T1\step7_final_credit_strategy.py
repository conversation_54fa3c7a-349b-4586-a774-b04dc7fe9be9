"""
步骤7：基于AHP排名制定最终信贷投放策略
将99家企业分为三个风险等级，并分配相应的利率（4%-15%）
"""
import pandas as pd
import numpy as np

print("="*80)
print("步骤7：制定最终信贷投放策略")
print("="*80)

# 读取AHP综合评估结果
ahp_results = pd.read_csv('符合条件企业_AHP综合评估结果.csv')
print(f"参与信贷投放的企业数量: {len(ahp_results)}家")

# 基于AHP排名和信誉评级制定风险等级和利率策略
def assign_risk_level_and_rate(row):
    """根据AHP排名和信誉评级分配风险等级和利率"""
    ranking = row['AHP排名']
    rating = row['信誉评级']
    
    # 风险等级划分逻辑：
    # - 前33%（1-33名）：低风险
    # - 中33%（34-66名）：中风险  
    # - 后33%（67-99名）：高风险
    
    if ranking <= 33:
        risk_level = "低风险"
        if rating == 'A':
            interest_rate = 4.0 + (ranking - 1) * 0.05  # 4.0%-5.6%
        elif rating == 'B':
            interest_rate = 4.5 + (ranking - 1) * 0.05  # 4.5%-6.1%
        else:  # C
            interest_rate = 5.0 + (ranking - 1) * 0.05  # 5.0%-6.6%
    elif ranking <= 66:
        risk_level = "中风险"
        if rating == 'A':
            interest_rate = 6.0 + (ranking - 34) * 0.08  # 6.0%-8.6%
        elif rating == 'B':
            interest_rate = 7.0 + (ranking - 34) * 0.08  # 7.0%-9.6%
        else:  # C
            interest_rate = 8.0 + (ranking - 34) * 0.08  # 8.0%-10.6%
    else:  # 67-99
        risk_level = "高风险"
        if rating == 'A':
            interest_rate = 9.0 + (ranking - 67) * 0.12  # 9.0%-12.8%
        elif rating == 'B':
            interest_rate = 10.0 + (ranking - 67) * 0.12  # 10.0%-13.8%
        else:  # C
            interest_rate = 11.0 + (ranking - 67) * 0.12  # 11.0%-14.8%
    
    # 确保利率在4%-15%范围内
    interest_rate = max(4.0, min(15.0, interest_rate))
    
    return risk_level, round(interest_rate, 2)

# 应用策略
ahp_results[['风险等级', '建议利率(%)']] = ahp_results.apply(
    lambda row: pd.Series(assign_risk_level_and_rate(row)), axis=1
)

# 添加额外分析列
ahp_results['建议放贷'] = '是'  # 所有99家企业都建议放贷（已排除D级）

print(f"\n✅ 信贷投放策略概览:")
strategy_summary = ahp_results.groupby(['风险等级', '信誉评级']).agg({
    '企业代号': 'count',
    '建议利率(%)': ['mean', 'min', 'max']
}).round(2)
strategy_summary.columns = ['企业数量', '平均利率', '最低利率', '最高利率']
print(strategy_summary)

# 按风险等级统计
print(f"\n按风险等级统计:")
risk_stats = ahp_results['风险等级'].value_counts()
for risk_level in ['低风险', '中风险', '高风险']:
    count = risk_stats.get(risk_level, 0)
    subset = ahp_results[ahp_results['风险等级'] == risk_level]
    avg_rate = subset['建议利率(%)'].mean()
    print(f"  {risk_level}: {count}家企业，平均利率 {avg_rate:.2f}%")

print(f"\n利率分布统计:")
print(f"  最低利率: {ahp_results['建议利率(%)'].min():.2f}%")
print(f"  最高利率: {ahp_results['建议利率(%)'].max():.2f}%")
print(f"  平均利率: {ahp_results['建议利率(%)'].mean():.2f}%")

# 显示完整策略结果（前20名和后10名）
print(f"\n前20名企业信贷策略:")
print("排名 | 企业 | AHP得分 | 信誉评级 | 风险等级 | 建议利率")
print("-" * 55)
for idx, row in ahp_results.head(20).iterrows():
    print(f"{row['AHP排名']:>3d} | {row['企业代号']:>4s} | {row['AHP综合得分']:7.4f} | {row['信誉评级']:>6s} | {row['风险等级']:>4s} | {row['建议利率(%)']:6.2f}%")

print(f"\n后10名企业信贷策略:")
print("排名 | 企业 | AHP得分 | 信誉评级 | 风险等级 | 建议利率")
print("-" * 55)
for idx, row in ahp_results.tail(10).iterrows():
    print(f"{row['AHP排名']:>3d} | {row['企业代号']:>4s} | {row['AHP综合得分']:7.4f} | {row['信誉评级']:>6s} | {row['风险等级']:>4s} | {row['建议利率(%)']:6.2f}%")

# 保存最终策略结果
ahp_results.to_csv('最终信贷投放策略.csv', index=False, encoding='utf-8-sig')
print(f"\n✅ 最终信贷投放策略已保存至: 最终信贷投放策略.csv")

# 策略验证
print(f"\n✅ 策略验证:")
print(f"- 企业总数: {len(ahp_results)}家 (排除D级后的全部企业)")
print(f"- 利率范围: {ahp_results['建议利率(%)'].min():.2f}%-{ahp_results['建议利率(%)'].max():.2f}% (符合4%-15%要求)")
print(f"- 建议放贷: {ahp_results['建议放贷'].sum()}家 (100%符合条件)")
print(f"- AHP排名与利率负相关: {ahp_results['AHP排名'].corr(ahp_results['建议利率(%)']):.4f}")

print(f"\n" + "="*80)
print("🎉 完整信贷决策分析已完成!")
print("✅ 从123家企业中筛选出99家符合条件的企业")
print("✅ 基于八项指标的AHP综合评估")
print("✅ 差异化风险等级划分和利率定价")
print("✅ 排除所有D级企业，确保贷款安全性")
print("="*80)
