"""
分析盈利能力指标正负值对标准化和评估方法的影响
"""
import pandas as pd
import numpy as np
from sklearn.preprocessing import MinMaxScaler, StandardScaler

print("="*70)
print("盈利能力指标正负值影响分析")
print("="*70)

# 读取数据
data = pd.read_csv('企业四项指标原始数据.csv')
profit = data['盈利能力']

print("1. 盈利能力指标分布分析")
print("="*40)
print(f"最小值: {profit.min():,.2f}")
print(f"最大值: {profit.max():,.2f}")
print(f"均值: {profit.mean():,.2f}")
print(f"中位数: {profit.median():,.2f}")
print(f"负值企业数量: {(profit < 0).sum()}")
print(f"正值企业数量: {(profit > 0).sum()}")
print(f"零值企业数量: {(profit == 0).sum()}")
print(f"负值比例: {(profit < 0).mean()*100:.1f}%")

print("\n负值企业详情:")
negative_companies = data[data['盈利能力'] < 0][['企业代号', '盈利能力', '最终信用分数']]
print(negative_companies.to_string(index=False))

print("\n2. 标准化方法对负值的处理")
print("="*40)

# Min-Max标准化处理负值
print("Min-Max标准化公式: x_scaled = (x - min) / (max - min)")
print("- 负值会被映射到[0,1]区间的较小值")
print("- 最负的值映射为0，最正的值映射为1")
print("- 保持相对关系不变")

min_val = profit.min()
max_val = profit.max()
print(f"\n映射示例:")
print(f"最负值 {min_val:,.0f} → 0.0000")
print(f"零值 0 → {(0 - min_val)/(max_val - min_val):.4f}")
print(f"最正值 {max_val:,.0f} → 1.0000")

# 实际标准化
scaler = MinMaxScaler()
profit_scaled = scaler.fit_transform(profit.values.reshape(-1, 1)).flatten()

print(f"\n实际标准化结果:")
for i, (company, original, scaled) in enumerate(zip(data['企业代号'], profit, profit_scaled)):
    if original < 0:
        print(f"{company}: {original:>12,.0f} → {scaled:.4f}")
    if i >= 10 and original >= 0:  # 只显示前几个负值和一些正值
        break

print("\n3. 对各评估方法的具体影响")
print("="*40)

print("""
🔍 层次分析法 (AHP):
   影响: ✅ 无问题
   - 负值被合理映射到0-1区间的低端
   - 盈利能力越差，得分越低，符合逻辑
   - 相对排序保持不变

🔍 TOPSIS法:
   影响: ✅ 无问题  
   - 标准化后所有值都为正，符合TOPSIS计算要求
   - 负盈利企业自然成为"远离理想解"的企业
   - 距离计算正常进行

🔍 熵权法:
   影响: ✅ 无问题
   - 标准化后所有值为正，熵计算正常
   - 盈利能力的变异程度得到正确反映
   - 权重确定不受负值影响

🔍 主成分分析法 (PCA):
   影响: ⚠️ 需要注意
   - Min-Max标准化: 将负值映射到[0,1]，改变了数据分布
   - Z-score标准化: 保持负值为负，可能更符合PCA原理
   - 但实际影响较小，排序基本一致
""")

print("\n4. 业务逻辑合理性分析")
print("="*40)

# 分析负盈利企业的其他指标
print("负盈利企业的综合表现:")
negative_analysis = data[data['盈利能力'] < 0][['企业代号', '最终信用分数', '盈利能力', '企业规模', '稳定性']]
print(negative_analysis.round(2).to_string(index=False))

print(f"\n负盈利企业信用分数分布:")
negative_credit = data[data['盈利能力'] < 0]['最终信用分数']
print(f"平均信用分数: {negative_credit.mean():.1f}")
print(f"信用分数分布: {dict(negative_credit.value_counts().sort_index())}")

print(f"\n所有企业信用分数分布:")
all_credit = data['最终信用分数']
print(f"平均信用分数: {all_credit.mean():.1f}")
print(f"信用分数分布: {dict(all_credit.value_counts().sort_index())}")

print("\n5. 标准化合理性验证")
print("="*40)

# 检查标准化后的排序是否合理
profit_with_scaled = pd.DataFrame({
    '企业代号': data['企业代号'],
    '原始盈利': profit,
    '标准化盈利': profit_scaled,
    '信用分数': data['最终信用分数']
})

# 按原始盈利排序
profit_sorted = profit_with_scaled.sort_values('原始盈利')
print("盈利能力排序验证（显示最差和最好的企业）:")
print("最差的5个企业:")
print(profit_sorted.head().to_string(index=False))
print("\n最好的5个企业:")
print(profit_sorted.tail().to_string(index=False))

# 检查标准化是否保持排序
original_rank = profit.rank()
scaled_rank = pd.Series(profit_scaled).rank()
rank_correlation = np.corrcoef(original_rank, scaled_rank)[0, 1]
print(f"\n排序一致性验证:")
print(f"原始数据与标准化数据排序相关性: {rank_correlation:.6f}")

print("\n6. 结论与建议")
print("="*40)

if rank_correlation > 0.999:
    print("✅ 标准化处理负值完全合理:")
    print("   - 保持了企业间的相对排序")
    print("   - 负盈利企业得到合理的低分")
    print("   - 所有评估方法都能正常工作")
    print("   - 业务逻辑清晰：盈利越差，评估得分越低")
else:
    print("⚠️ 标准化可能存在问题，需要进一步检查")

print(f"""
💡 处理负值的方案对比:

1. 当前方案 (Min-Max标准化):
   ✅ 将负值映射到[0,1]区间
   ✅ 保持相对关系
   ✅ 适用于所有评估方法
   ✅ 业务含义清晰

2. 替代方案:
   - 加常数法: 所有值加上|min|+1
   - Z-score标准化: 保持负值
   - 分段标准化: 正负值分别处理

3. 推荐方案:
   ✅ 继续使用当前Min-Max标准化
   - 理论合理，实践可行
   - 负值处理恰当
   - 评估结果符合业务逻辑
""")

# 对比不同处理方法
print(f"\n7. 不同负值处理方法对比")
print("="*40)

# 方法1: 当前的Min-Max
method1 = profit_scaled

# 方法2: 先加常数再标准化
profit_shifted = profit - profit.min() + 1
method2 = MinMaxScaler().fit_transform(profit_shifted.values.reshape(-1, 1)).flatten()

# 方法3: Z-score标准化
method3 = StandardScaler().fit_transform(profit.values.reshape(-1, 1)).flatten()

print("三种方法的相关性:")
corr_12 = np.corrcoef(method1, method2)[0, 1]
corr_13 = np.corrcoef(method1, method3)[0, 1]
corr_23 = np.corrcoef(method2, method3)[0, 1]

print(f"Min-Max vs 加常数法: {corr_12:.6f}")
print(f"Min-Max vs Z-score: {corr_13:.6f}")
print(f"加常数法 vs Z-score: {corr_23:.6f}")

if min(corr_12, corr_13, corr_23) > 0.95:
    print("✅ 所有方法高度相关，当前方案可靠")
