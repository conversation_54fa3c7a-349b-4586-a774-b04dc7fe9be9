"""
基于修正后的AHP策略重新生成差异化利率分配
使用正确的AHP排名和风险等级
"""
import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

print("="*80)
print("基于修正AHP策略的差异化利率分配方案")
print("="*80)

# 读取修正后的AHP策略
df = pd.read_csv('AHP层次分析法信贷分配策略_最终修正版.csv', encoding='utf-8-sig')

print("1. 利率分配策略设计")
print("="*50)
print("利率区间: 4% - 15%")
print("分配原则: AHP得分越高，排名越靠前，利率越低")
print("风险调整: 基于修正后的风险等级进行微调")

# 利率分配参数
MIN_RATE = 0.04  # 最低利率4%
MAX_RATE = 0.15  # 最高利率15%
RATE_RANGE = MAX_RATE - MIN_RATE  # 利率区间11%

print(f"\n2. 利率计算方法")
print("="*50)

def calculate_corrected_interest_rates(strategy_data):
    """
    基于修正后的AHP排名和风险等级计算差异化利率
    """
    total_companies = len(strategy_data)
    
    # 基于正确排名计算基础利率
    # 排名1（得分最高）对应4%，排名123（得分最低）对应15%
    base_rates = []
    
    for _, row in strategy_data.iterrows():
        rank = row['AHP排名']
        # 线性插值：排名越靠前，利率越低
        base_rate = MIN_RATE + (rank - 1) * RATE_RANGE / (total_companies - 1)
        base_rates.append(base_rate)
    
    # 基于风险等级的微调
    risk_adjustments = {
        '低风险': -0.002,     # 优惠0.2个百分点
        '中等风险': 0.0,      # 无调整
        '高风险': 0.005,      # 上浮0.5个百分点
        '极高风险': 0.01      # 上浮1个百分点
    }
    
    final_rates = []
    for i, (_, row) in enumerate(strategy_data.iterrows()):
        base_rate = base_rates[i]
        risk_level = row['风险等级']
        adjustment = risk_adjustments.get(risk_level, 0)
        
        final_rate = base_rate + adjustment
        
        # 确保利率在4%-15%范围内
        final_rate = max(MIN_RATE, min(MAX_RATE, final_rate))
        final_rates.append(final_rate)
    
    return base_rates, final_rates

# 计算利率
base_rates, final_rates = calculate_corrected_interest_rates(df)

# 添加利率信息
df['基础利率(%)'] = [rate * 100 for rate in base_rates]
df['最终利率(%)'] = [rate * 100 for rate in final_rates]

print("✅ 利率分配计算完成")

print(f"\n3. 利率分配结果统计")
print("="*50)

print(f"利率统计信息:")
print(f"最低利率: {min(final_rates)*100:.2f}%")
print(f"最高利率: {max(final_rates)*100:.2f}%")
print(f"平均利率: {np.mean(final_rates)*100:.2f}%")
print(f"中位数利率: {np.median(final_rates)*100:.2f}%")
print(f"利率标准差: {np.std(final_rates)*100:.2f}%")

# 按风险等级统计利率
print(f"\n各风险等级利率统计:")
risk_rate_stats = df.groupby('风险等级')['最终利率(%)'].agg(['count', 'mean', 'min', 'max']).round(2)
risk_rate_stats.columns = ['企业数量', '平均利率(%)', '最低利率(%)', '最高利率(%)']
print(risk_rate_stats.sort_values('平均利率(%)'))  # 按平均利率排序

print(f"\n4. 前30名优质企业利率方案")
print("="*80)

# 按AHP排名显示前30名
top_30 = df.sort_values('AHP排名').head(30)

print("排名  企业代号  AHP得分   分配比例(%)  风险等级   基础利率(%)  最终利率(%)")
print("-" * 80)

for _, row in top_30.iterrows():
    print(f"{row['AHP排名']:>3}   {row['企业代号']:>4}    {row['AHP综合得分']:>6.4f}    {row['最终分配比例(%)']:>7.4f}   {row['风险等级']:>6}   {row['基础利率(%)']:>7.2f}     {row['最终利率(%)']:>7.2f}")

print(f"\n5. 利率分布分析")
print("="*50)

# 创建利率区间统计
rate_bins = [4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]
rate_labels = ['4-5%', '5-6%', '6-7%', '7-8%', '8-9%', '9-10%', '10-11%', '11-12%', '12-13%', '13-14%', '14-15%']

df['利率区间'] = pd.cut(df['最终利率(%)'], bins=rate_bins, labels=rate_labels, include_lowest=True)

rate_distribution = df.groupby('利率区间').agg({
    '企业代号': 'count',
    '最终分配比例(%)': 'sum'
}).round(2)
rate_distribution.columns = ['企业数量', '分配比例总和(%)']

print("利率区间分布:")
print(rate_distribution)

print(f"\n6. 加权平均利率分析")
print("="*50)

# 计算加权平均利率（基于分配比例权重）
weighted_avg_rate = (df['最终利率(%)'] * df['最终分配比例(%)']).sum() / df['最终分配比例(%)'].sum()

print(f"加权平均利率: {weighted_avg_rate:.2f}%")

# 各风险等级收益权重分析
risk_revenue_weight = df.groupby('风险等级').agg({
    '最终分配比例(%)': 'sum',
    '最终利率(%)': 'mean'
}).round(2)
risk_revenue_weight.columns = ['分配权重(%)', '平均利率(%)']
risk_revenue_weight = risk_revenue_weight.sort_values('平均利率(%)')  # 按利率排序

print(f"\n各风险等级分配权重和利率:")
for risk_level, row in risk_revenue_weight.iterrows():
    print(f"{risk_level:>8}: 分配权重{row['分配权重(%)']:>6.2f}%, 平均利率{row['平均利率(%)']:>6.2f}%")

print(f"\n7. 优质客户vs高风险客户对比")
print("="*50)

# 分析前20%企业的利率情况
top_20_percent = int(len(df) * 0.2)
premium_clients = df.sort_values('AHP排名').head(top_20_percent)

print(f"前20%优质客户({top_20_percent}家):")
print(f"平均利率: {premium_clients['最终利率(%)'].mean():.2f}%")
print(f"利率范围: {premium_clients['最终利率(%)'].min():.2f}% - {premium_clients['最终利率(%)'].max():.2f}%")
print(f"分配比例总和: {premium_clients['最终分配比例(%)'].sum():.2f}%")

# 分析高风险客户利率
high_risk_clients = df[df['风险等级'].isin(['高风险', '极高风险'])]
print(f"\n高风险客户({len(high_risk_clients)}家):")
print(f"平均利率: {high_risk_clients['最终利率(%)'].mean():.2f}%")
print(f"利率范围: {high_risk_clients['最终利率(%)'].min():.2f}% - {high_risk_clients['最终利率(%)'].max():.2f}%")
print(f"分配比例总和: {high_risk_clients['最终分配比例(%)'].sum():.2f}%")

print(f"\n利率差异: {high_risk_clients['最终利率(%)'].mean() - premium_clients['最终利率(%)'].mean():.2f}个百分点")

print(f"\n8. 保存修正后的利率分配结果")
print("="*50)

# 按AHP排名重新排序
final_result = df.sort_values('AHP排名')[['企业代号', 'AHP综合得分', 'AHP排名', '最终分配比例(%)', 
                                          '风险等级', '基础利率(%)', '最终利率(%)']]

# 保存详细结果
final_result.round(4).to_csv('AHP修正版_差异化利率分配策略.csv', index=False, encoding='utf-8-sig')
print("✅ 修正版利率策略已保存至: AHP修正版_差异化利率分配策略.csv")

# 创建简化版给银行使用
bank_summary = final_result[['企业代号', 'AHP排名', '最终分配比例(%)', '最终利率(%)', '风险等级']].copy()
bank_summary.columns = ['企业代号', '信用排名', '信贷分配比例(%)', '执行年利率(%)', '风险等级']
bank_summary.to_csv('银行执行版_企业利率表_修正版.csv', index=False, encoding='utf-8-sig')
print("✅ 银行执行版利率表已保存至: 银行执行版_企业利率表_修正版.csv")

print(f"\n9. 修正后策略执行建议")
print("="*50)
print(f"""
🎯 修正后差异化利率策略:

📊 利率结构特点:
• 利率区间: 4.00% - 15.00%
• 加权平均利率: {weighted_avg_rate:.2f}%
• 逻辑正确: 得分高→排名前→风险低→利率低

💰 分配与利率匹配:
• 低风险客户: {risk_revenue_weight.loc['低风险', '分配权重(%)']:.1f}% (平均利率{risk_revenue_weight.loc['低风险', '平均利率(%)']:.2f}%)
• 中等风险客户: {risk_revenue_weight.loc['中等风险', '分配权重(%)']:.1f}% (平均利率{risk_revenue_weight.loc['中等风险', '平均利率(%)']:.2f}%)
• 高风险客户: {risk_revenue_weight.loc['高风险', '分配权重(%)']:.1f}% (平均利率{risk_revenue_weight.loc['高风险', '平均利率(%)']:.2f}%)
• 极高风险客户: {risk_revenue_weight.loc['极高风险', '分配权重(%)']:.1f}% (平均利率{risk_revenue_weight.loc['极高风险', '平均利率(%)']:.2f}%)

✅ 关键修正:
1. AHP排名现在完全基于AHP综合得分
2. 风险等级分配符合常理：得分高风险低
3. 利率分配合理：排名前利率低，风险小利率低
4. 分配权重匹配：低风险客户获得更多资金和更低利率

🏆 最优配置示例:
• 企业{final_result.iloc[0]['企业代号']}: 排名1, 得分{final_result.iloc[0]['AHP综合得分']:.4f}, {final_result.iloc[0]['风险等级']}, 分配{final_result.iloc[0]['最终分配比例(%)']:.3f}%, 利率{final_result.iloc[0]['最终利率(%)']:.2f}%

📋 现在的策略逻辑完全正确！
""")

print("="*80)
print("修正后的AHP差异化利率分配策略制定完成！")
print("="*80)
