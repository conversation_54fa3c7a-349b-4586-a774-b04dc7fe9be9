import pandas as pd
import numpy as np
from datetime import datetime

# 读取数据
print("正在读取数据...")
# 进项发票数据 (2.csv)
inbound_df = pd.read_csv('T1/2.csv')
# 销项发票数据 (3.csv)  
outbound_df = pd.read_csv('T1/3.csv')

print(f"进项发票数据行数: {len(inbound_df)}")
print(f"销项发票数据行数: {len(outbound_df)}")

# 数据预处理
def preprocess_data(df, invoice_type):
    # 转换日期格式
    df['开票日期'] = pd.to_datetime(df['开票日期'])
    
    # 提取年月
    df['年月'] = df['开票日期'].dt.to_period('M')
    
    # 只保留有效发票
    df_valid = df[df['发票状态'] == '有效发票'].copy()
    
    print(f"{invoice_type}有效发票数据行数: {len(df_valid)}")
    
    return df_valid

# 预处理数据
inbound_valid = preprocess_data(inbound_df, "进项")
outbound_valid = preprocess_data(outbound_df, "销项")

# 按企业和月份汇总进项发票
print("正在汇总进项发票...")
inbound_monthly = inbound_valid.groupby(['企业代号', '年月'])['价税合计'].sum().reset_index()
inbound_monthly.rename(columns={'价税合计': '进项价税合计'}, inplace=True)

# 按企业和月份汇总销项发票
print("正在汇总销项发票...")
outbound_monthly = outbound_valid.groupby(['企业代号', '年月'])['价税合计'].sum().reset_index()
outbound_monthly.rename(columns={'价税合计': '销项价税合计'}, inplace=True)

# 合并进项和销项数据
print("正在合并数据...")
monthly_combined = pd.merge(inbound_monthly, outbound_monthly, on=['企业代号', '年月'], how='outer')

# 填充缺失值为0
monthly_combined['进项价税合计'] = monthly_combined['进项价税合计'].fillna(0)
monthly_combined['销项价税合计'] = monthly_combined['销项价税合计'].fillna(0)

# 计算每月的差值 (销项 - 进项)
monthly_combined['月度差值'] = monthly_combined['销项价税合计'] - monthly_combined['进项价税合计']

print("月度汇总数据示例:")
print(monthly_combined.head(10))

# 计算每个企业每月差值的中位数
print("正在计算每个企业的中位数...")
company_median = monthly_combined.groupby('企业代号')['月度差值'].median().reset_index()
company_median.rename(columns={'月度差值': '月度差值中位数'}, inplace=True)

# 计算统计信息
print("正在计算统计信息...")
stats = monthly_combined.groupby('企业代号')['月度差值'].agg([
    'count', 'mean', 'median', 'std', 'min', 'max'
]).reset_index()

# 合并结果
result = pd.merge(company_median, stats, on='企业代号')

print(f"\n计算完成！共有 {len(result)} 家企业的数据")
print("\n结果示例:")
print(result.head(10))

# 保存结果
result.to_csv('企业月度差值中位数结果.csv', index=False, encoding='utf-8-sig')
print(f"\n结果已保存到: 企业月度差值中位数结果.csv")

# 显示一些关键统计信息
print(f"\n=== 整体统计信息 ===")
print(f"中位数的平均值: {result['月度差值中位数'].mean():.2f}")
print(f"中位数的标准差: {result['月度差值中位数'].std():.2f}")
print(f"中位数的最小值: {result['月度差值中位数'].min():.2f}")
print(f"中位数的最大值: {result['月度差值中位数'].max():.2f}")

print(f"\n中位数为正值的企业数量: {(result['月度差值中位数'] > 0).sum()}")
print(f"中位数为负值的企业数量: {(result['月度差值中位数'] < 0).sum()}")
print(f"中位数为零的企业数量: {(result['月度差值中位数'] == 0).sum()}")
