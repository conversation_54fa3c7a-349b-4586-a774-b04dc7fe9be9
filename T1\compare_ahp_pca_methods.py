"""
AHP方法 vs PCA方法的信贷策略对比分析
"""
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

print("="*80)
print("AHP方法 vs PCA方法信贷策略对比分析")
print("="*80)

# 读取两种方法的结果
ahp_results = pd.read_csv('最终正确的信贷投放策略.csv')
pca_results = pd.read_csv('PCA方法信贷投放策略.csv')

print(f"AHP方法企业数: {len(ahp_results)}家")
print(f"PCA方法企业数: {len(pca_results)}家")

# 合并两种方法的结果
comparison = pd.merge(
    ahp_results[['企业代号', 'AHP综合得分', 'AHP排名', '建议利率(%)', '风险等级']],
    pca_results[['企业代号', 'PCA综合得分', 'PCA排名']],
    on='企业代号',
    suffixes=('_AHP', '_PCA')
).copy()

comparison = pd.merge(comparison, pca_results[['企业代号', '建议利率(%)', '风险等级']], 
                     on='企业代号', suffixes=('', '_PCA'))
comparison.rename(columns={'建议利率(%)_PCA': 'PCA建议利率(%)', '风险等级_PCA': 'PCA风险等级'}, inplace=True)

print(f"\n✅ 两种方法对比统计:")

# 1. 相关性分析
score_corr = comparison['AHP综合得分'].corr(comparison['PCA综合得分'])
ranking_corr = comparison['AHP排名'].corr(comparison['PCA排名'])
rate_corr = comparison['建议利率(%)'].corr(comparison['PCA建议利率(%)'])

print(f"得分相关性: {score_corr:.4f}")
print(f"排名相关性: {ranking_corr:.4f}")  
print(f"利率相关性: {rate_corr:.4f}")

# 2. 利率分布对比
print(f"\n利率分布对比:")
print(f"AHP方法: 平均{comparison['建议利率(%)'].mean():.2f}%, 范围{comparison['建议利率(%)'].min():.2f}%-{comparison['建议利率(%)'].max():.2f}%")
print(f"PCA方法: 平均{comparison['PCA建议利率(%)'].mean():.2f}%, 范围{comparison['PCA建议利率(%)'].min():.2f}%-{comparison['PCA建议利率(%)'].max():.2f}%")

# 3. 风险等级分布对比
print(f"\nAHP方法风险等级分布:")
ahp_risk_dist = comparison['风险等级'].value_counts()
print(ahp_risk_dist)

print(f"\nPCA方法风险等级分布:")
pca_risk_dist = comparison['PCA风险等级'].value_counts()
print(pca_risk_dist)

# 4. 排名差异分析
comparison['排名差异'] = abs(comparison['AHP排名'] - comparison['PCA排名'])
comparison['利率差异'] = abs(comparison['建议利率(%)'] - comparison['PCA建议利率(%)'])

print(f"\n排名差异统计:")
print(f"平均差异: {comparison['排名差异'].mean():.1f}名")
print(f"最大差异: {comparison['排名差异'].max()}名")
print(f"差异超过20名的企业: {(comparison['排名差异'] > 20).sum()}家")

print(f"\n利率差异统计:")
print(f"平均差异: {comparison['利率差异'].mean():.2f}%")
print(f"最大差异: {comparison['利率差异'].max():.2f}%")
print(f"差异超过2%的企业: {(comparison['利率差异'] > 2).sum()}家")

# 5. 前20名企业对比
print(f"\n🏆 前20名企业对比（按AHP排名）:")
print("排名 | 企业 | AHP得分 | PCA得分 | AHP利率 | PCA利率 | 排名差异")
print("-" * 65)

top20_ahp = comparison.nsmallest(20, 'AHP排名')
for _, row in top20_ahp.iterrows():
    print(f"{row['AHP排名']:>3d} | {row['企业代号']:>4s} | {row['AHP综合得分']:7.4f} | {row['PCA综合得分']:7.4f} | "
          f"{row['建议利率(%)']:6.2f}% | {row['PCA建议利率(%)']:6.2f}% | {row['排名差异']:>4d}")

# 6. 差异最大的企业
print(f"\n⚠️  排名差异最大的10家企业:")
print("企业 | AHP排名 | PCA排名 | AHP利率 | PCA利率 | 排名差异 | 利率差异")
print("-" * 70)

max_diff = comparison.nlargest(10, '排名差异')
for _, row in max_diff.iterrows():
    print(f"{row['企业代号']:>4s} | {row['AHP排名']:>6d} | {row['PCA排名']:>6d} | "
          f"{row['建议利率(%)']:6.2f}% | {row['PCA建议利率(%)']:6.2f}% | {row['排名差异']:>6d} | {row['利率差异']:6.2f}%")

# 7. 方法优劣势分析
print(f"\n✅ 方法对比总结:")
print(f"""
🔵 AHP方法特点:
- 主观赋权，权重明确可解释
- 综合得分分布相对均匀 (0.3064-0.6945)
- 更多低风险企业 (23家 vs 16家)
- 平均利率较低 (8.42% vs 10.25%)
- 适合有明确业务优先级的场景

🟡 PCA方法特点:
- 客观降维，基于数据内在结构
- 前4个主成分解释85.89%方差
- 更多高风险企业 (56家 vs 5家)
- 平均利率较高 (10.25% vs 8.42%)
- 适合探索性分析和数据驱动决策

📊 方法一致性:
- 排名相关性: {ranking_corr:.4f} (中等相关)
- 利率相关性: {rate_corr:.4f} (高度相关)
- 两种方法在整体趋势上基本一致

💡 建议:
- 如果业务重点明确，推荐使用AHP方法
- 如果需要客观分析，可考虑PCA方法
- 也可以结合两种方法的结果制定综合策略
""")

# 保存对比结果
comparison.to_csv('AHP与PCA方法对比结果.csv', index=False, encoding='utf-8-sig')
print(f"\n✅ 对比分析结果已保存至: AHP与PCA方法对比结果.csv")

# 关键发现
print(f"\n🔍 关键发现:")

# 找出两种方法都认为最优的企业
top10_both = set(comparison.nsmallest(10, 'AHP排名')['企业代号']) & set(comparison.nsmallest(10, 'PCA排名')['企业代号'])
print(f"两种方法都认为是前10名的企业: {sorted(list(top10_both))}")

# 找出分歧最大的企业
high_ahp_low_pca = comparison[(comparison['AHP排名'] <= 20) & (comparison['PCA排名'] >= 50)]
print(f"AHP高排名但PCA低排名的企业: {list(high_ahp_low_pca['企业代号'])}")

low_ahp_high_pca = comparison[(comparison['AHP排名'] >= 50) & (comparison['PCA排名'] <= 20)]
print(f"AHP低排名但PCA高排名的企业: {list(low_ahp_high_pca['企业代号'])}")

print("="*80)
