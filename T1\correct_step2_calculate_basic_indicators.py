"""
重新计算正确的基础四项指标：负债水平、盈利能力、现金流稳定性、企业规模
基于有效发票数据，仅针对符合条件的99家企业（排除D级）
"""
import pandas as pd
import numpy as np

print("="*80)
print("重新计算正确的基础四项指标")
print("="*80)

# 读取符合条件的企业名单
eligible_enterprises = pd.read_csv('符合条件企业名单.csv')
eligible_company_list = eligible_enterprises['企业代号'].tolist()
print(f"符合条件企业数量: {len(eligible_company_list)}家")

# 读取发票数据（只保留符合条件企业的有效发票）
data_input = pd.read_csv('2.csv')
data_output = pd.read_csv('3.csv')

# 筛选符合条件企业的有效发票
data_input_valid = data_input[
    (data_input['企业代号'].isin(eligible_company_list)) &
    (data_input['发票状态'] == '有效发票')
].copy()

data_output_valid = data_output[
    (data_output['企业代号'].isin(eligible_company_list)) &
    (data_output['发票状态'] == '有效发票')
].copy()

print(f"有效进项发票: {len(data_input_valid)}条")
print(f"有效销项发票: {len(data_output_valid)}条")

def calculate_correct_basic_indicators(company_list, input_data, output_data):
    """计算正确的基础四项指标"""
    results = {}
    
    print("  正在计算基础四项指标...")
    
    for i, company in enumerate(company_list):
        if (i + 1) % 20 == 0:
            print(f"    已处理: {i+1}/{len(company_list)} 家企业")
            
        # 获取该企业的有效发票数据
        company_input = input_data[input_data['企业代号'] == company].copy()
        company_output = output_data[output_data['企业代号'] == company].copy()
        
        if len(company_input) == 0 and len(company_output) == 0:
            results[company] = {
                '负债水平': 0.5,  # 默认中等负债水平
                '盈利能力': 0,
                '现金流稳定性': 0,
                '企业规模': 0
            }
            continue
            
        # 按月汇总数据
        # 进项按月汇总
        if len(company_input) > 0:
            company_input['年月'] = pd.to_datetime(company_input['开票日期']).dt.to_period('M')
            monthly_input = company_input.groupby('年月').agg({
                '金额': 'sum',
                '税额': 'sum'
            }).reset_index()
            monthly_input['进项价税合计'] = monthly_input['金额'] + monthly_input['税额']
            monthly_input['进项税额'] = monthly_input['税额']
        else:
            monthly_input = pd.DataFrame(columns=['年月', '进项价税合计', '进项税额'])
            
        # 销项按月汇总
        if len(company_output) > 0:
            company_output['年月'] = pd.to_datetime(company_output['开票日期']).dt.to_period('M')
            monthly_output = company_output.groupby('年月').agg({
                '金额': 'sum',
                '税额': 'sum'
            }).reset_index()
            monthly_output['销项价税合计'] = monthly_output['金额'] + monthly_output['税额']
            monthly_output['销项税额'] = monthly_output['税额']
        else:
            monthly_output = pd.DataFrame(columns=['年月', '销项价税合计', '销项税额'])
            
        # 合并月度数据
        if len(monthly_input) > 0 and len(monthly_output) > 0:
            monthly_combined = pd.merge(monthly_input[['年月', '进项价税合计', '进项税额']], 
                                      monthly_output[['年月', '销项价税合计', '销项税额']], 
                                      on='年月', how='outer')
        elif len(monthly_input) > 0:
            monthly_combined = monthly_input[['年月', '进项价税合计', '进项税额']].copy()
            monthly_combined['销项价税合计'] = 0
            monthly_combined['销项税额'] = 0
        elif len(monthly_output) > 0:
            monthly_combined = monthly_output[['年月', '销项价税合计', '销项税额']].copy()
            monthly_combined['进项价税合计'] = 0
            monthly_combined['进项税额'] = 0
        else:
            monthly_combined = pd.DataFrame()
            
        monthly_combined = monthly_combined.fillna(0)
        
        if len(monthly_combined) == 0:
            results[company] = {
                '负债水平': 0.5,
                '盈利能力': 0,
                '现金流稳定性': 0,
                '企业规模': 0
            }
            continue
            
        # 1. 负债水平 = 进项税额总和 / (销项税额总和 + 1)
        # 比值越高说明负债（进项）相对销项越多，负债水平越高（负向指标）
        total_input_tax = monthly_combined['进项税额'].sum()
        total_output_tax = monthly_combined['销项税额'].sum()
        debt_level = total_input_tax / (total_output_tax + 1)  # 避免除零
        
        # 2. 盈利能力 = 月度差值中位数 (销项价税合计 - 进项价税合计)
        monthly_combined['月度差值'] = monthly_combined['销项价税合计'] - monthly_combined['进项价税合计']
        profit_ability = monthly_combined['月度差值'].median()
        
        # 3. 现金流稳定性 = 1 / (1 + 月度差值的变异系数)
        # 变异系数越小，现金流越稳定，稳定性指标值越高
        if monthly_combined['月度差值'].std() > 0 and monthly_combined['月度差值'].mean() != 0:
            cv = abs(monthly_combined['月度差值'].std() / monthly_combined['月度差值'].mean())
            cash_flow_stability = 1 / (1 + cv)
        else:
            cash_flow_stability = 0.5  # 默认中等稳定性
        
        # 4. 企业规模 = 月度绝对值之和的平均值
        monthly_combined['月度绝对值之和'] = (monthly_combined['进项价税合计'].abs() + 
                                    monthly_combined['销项价税合计'].abs())
        enterprise_scale = monthly_combined['月度绝对值之和'].mean()
        
        results[company] = {
            '负债水平': debt_level,
            '盈利能力': profit_ability,
            '现金流稳定性': cash_flow_stability,
            '企业规模': enterprise_scale
        }
    
    print(f"    基础四项指标计算完成: {len(company_list)}家企业")
    return results

# 计算基础四项指标
basic_indicators = calculate_correct_basic_indicators(eligible_company_list, data_input_valid, data_output_valid)

# 转换为DataFrame
basic_df = pd.DataFrame.from_dict(basic_indicators, orient='index').reset_index()
basic_df.rename(columns={'index': '企业代号'}, inplace=True)

print(f"\n基础四项指标统计摘要:")
for col in ['负债水平', '盈利能力', '现金流稳定性', '企业规模']:
    print(f"{col}: 最小值={basic_df[col].min():.4f}, 最大值={basic_df[col].max():.4f}, 平均值={basic_df[col].mean():.4f}")

# 保存基础四项指标
basic_df.to_csv('符合条件企业_正确的基础四项指标.csv', index=False, encoding='utf-8-sig')
print(f"\n✅ 正确的基础四项指标已保存至: 符合条件企业_正确的基础四项指标.csv")

print(f"\n前10家企业基础四项指标预览:")
print("企业代号 | 负债水平 | 盈利能力 | 现金流稳定性 | 企业规模")
print("-" * 65)
for _, row in basic_df.head(10).iterrows():
    print(f"{row['企业代号']:>6} | {row['负债水平']:>8.4f} | {row['盈利能力']:>10.0f} | {row['现金流稳定性']:>10.4f} | {row['企业规模']:>10.0f}")

print(f"\n下一步：合并正确的基础指标和税务指标")
