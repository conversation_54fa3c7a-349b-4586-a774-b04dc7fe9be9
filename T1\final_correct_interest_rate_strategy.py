"""
基于正确的AHP排名依次分配利率（4%-15%区间）
采用得分驱动的连续利率分配策略
"""
import pandas as pd
import numpy as np

print("="*80)
print("基于AHP排名的利率分配策略")
print("="*80)

# 读取AHP综合评估结果
ahp_results = pd.read_csv('正确的AHP综合评估结果.csv')
print(f"参与利率分配的企业数量: {len(ahp_results)}家")

print(f"\nAHP得分分布:")
print(f"最高分: {ahp_results['AHP综合得分'].max():.4f} (排名1)")
print(f"最低分: {ahp_results['AHP综合得分'].min():.4f} (排名99)")
print(f"平均分: {ahp_results['AHP综合得分'].mean():.4f}")

def assign_interest_rate_by_score(score, max_score, min_score):
    """
    基于AHP综合得分线性分配利率
    得分越高，利率越低（4%-15%区间）
    """
    # 将得分标准化到[0,1]区间
    normalized_score = (score - min_score) / (max_score - min_score)
    
    # 线性映射到利率区间：得分高 -> 利率低
    # 最高分 -> 4%，最低分 -> 15%
    interest_rate = 4.0 + (1 - normalized_score) * 11.0
    
    return round(interest_rate, 2)

# 获取得分范围
max_score = ahp_results['AHP综合得分'].max()
min_score = ahp_results['AHP综合得分'].min()

# 分配利率
ahp_results['建议利率(%)'] = ahp_results['AHP综合得分'].apply(
    lambda x: assign_interest_rate_by_score(x, max_score, min_score)
)

# 添加风险等级（基于利率区间）
def assign_risk_level(rate):
    if rate <= 7.0:
        return "低风险"
    elif rate <= 11.0:
        return "中风险"
    else:
        return "高风险"

ahp_results['风险等级'] = ahp_results['建议利率(%)'].apply(assign_risk_level)
ahp_results['建议放贷'] = '是'  # 所有99家企业都建议放贷

print(f"\n✅ 利率分配验证:")
print(f"利率范围: {ahp_results['建议利率(%)'].min():.2f}% - {ahp_results['建议利率(%)'].max():.2f}%")
print(f"平均利率: {ahp_results['建议利率(%)'].mean():.2f}%")

# 按风险等级统计
risk_stats = ahp_results.groupby('风险等级').agg({
    '企业代号': 'count',
    '建议利率(%)': ['mean', 'min', 'max']
}).round(2)
risk_stats.columns = ['企业数量', '平均利率', '最低利率', '最高利率']

print(f"\n按风险等级统计:")
print(risk_stats)

# 按信誉评级分析
rating_stats = ahp_results.groupby('信誉评级').agg({
    '企业代号': 'count',
    '建议利率(%)': ['mean', 'min', 'max'],
    'AHP综合得分': ['mean', 'min', 'max']
}).round(4)
rating_stats.columns = ['企业数量', '平均利率', '最低利率', '最高利率', '平均得分', '最低得分', '最高得分']

print(f"\n按信誉评级统计:")
print(rating_stats)

# 显示完整策略结果
print(f"\n🎯 最终信贷投放策略 - 前30名企业:")
print("排名 | 企业 | AHP得分 | 信誉评级 | 风险等级 | 建议利率")
print("-" * 58)
for idx, row in ahp_results.head(30).iterrows():
    print(f"{row['AHP排名']:>3d} | {row['企业代号']:>4s} | {row['AHP综合得分']:7.4f} | {row['信誉评级']:>6s} | {row['风险等级']:>4s} | {row['建议利率(%)']:6.2f}%")

print(f"\n🎯 最终信贷投放策略 - 后20名企业:")
print("排名 | 企业 | AHP得分 | 信誉评级 | 风险等级 | 建议利率")
print("-" * 58)
for idx, row in ahp_results.tail(20).iterrows():
    print(f"{row['AHP排名']:>3d} | {row['企业代号']:>4s} | {row['AHP综合得分']:7.4f} | {row['信誉评级']:>6s} | {row['风险等级']:>4s} | {row['建议利率(%)']:6.2f}%")

# 保存最终策略结果
final_strategy = ahp_results[['企业代号', 'AHP综合得分', 'AHP排名', '信誉评级', 
                             '风险等级', '建议利率(%)', '建议放贷']].copy()

final_strategy.to_csv('最终正确的信贷投放策略.csv', index=False, encoding='utf-8-sig')
print(f"\n✅ 最终策略已保存至: 最终正确的信贷投放策略.csv")

# 验证策略合理性
print(f"\n✅ 策略验证:")
print(f"- 企业总数: {len(final_strategy)}家 (符合条件的全部企业)")
print(f"- 利率范围: {final_strategy['建议利率(%)'].min():.2f}%-{final_strategy['建议利率(%)'].max():.2f}% (符合4%-15%要求)")
print(f"- 排名与利率相关性: {final_strategy['AHP排名'].corr(final_strategy['建议利率(%)']):.4f} (正相关，排名越高利率越高)")
print(f"- 得分与利率相关性: {final_strategy['AHP综合得分'].corr(final_strategy['建议利率(%)']):.4f} (负相关，得分越高利率越低)")

# 关键企业展示
print(f"\n🏆 最优企业（前5名）:")
top5 = final_strategy.head(5)
for _, row in top5.iterrows():
    print(f"  {row['企业代号']} - 得分:{row['AHP综合得分']:.4f}, 利率:{row['建议利率(%)']}%, {row['信誉评级']}级")

print(f"\n⚠️  高风险企业（后5名）:")
bottom5 = final_strategy.tail(5)
for _, row in bottom5.iterrows():
    print(f"  {row['企业代号']} - 得分:{row['AHP综合得分']:.4f}, 利率:{row['建议利率(%)']}%, {row['信誉评级']}级")

print(f"\n" + "="*80)
print("🎉 完整信贷决策分析已正确完成！")
print("✅ 排除D级企业（24家，100%违约率）")
print("✅ 基于正确的八项指标体系进行AHP评估")
print("✅ 99家符合条件企业获得4.00%-15.00%的差异化利率")
print("✅ E42企业获得最优条件（得分0.6945，利率4.00%）")
print("="*80)
