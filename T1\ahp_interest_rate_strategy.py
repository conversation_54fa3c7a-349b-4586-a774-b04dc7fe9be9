"""
基于AHP层次分析法的差异化利率分配策略
根据企业AHP排名分配4%-15%的年利率
排名越高，利率越低
"""
import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

print("="*80)
print("基于AHP策略的差异化利率分配方案")
print("="*80)

# 读取AHP分配策略结果
try:
    ahp_strategy = pd.read_csv('AHP层次分析法信贷分配策略.csv', encoding='utf-8')
except UnicodeDecodeError:
    try:
        ahp_strategy = pd.read_csv('AHP层次分析法信贷分配策略.csv', encoding='gbk')
    except:
        ahp_strategy = pd.read_csv('AHP层次分析法信贷分配策略.csv', encoding='utf-8-sig')

print("1. 利率分配策略设计")
print("="*50)

print("利率区间: 4% - 15%")
print("分配原则: 排名越高，利率越低")
print("考虑因素: AHP排名、风险等级、分配比例")

# 利率分配参数
MIN_RATE = 0.04  # 最低利率4%
MAX_RATE = 0.15  # 最高利率15%
RATE_RANGE = MAX_RATE - MIN_RATE  # 利率区间11%

print(f"\n2. 利率分配计算")
print("="*50)

def calculate_interest_rates(strategy_data):
    """
    基于AHP排名和风险等级计算差异化利率
    """
    # 获取总企业数
    total_companies = len(strategy_data)
    
    # 基于排名计算基础利率（线性分布）
    # 排名1的企业利率最低，排名最后的企业利率最高
    base_rates = []
    
    for idx, row in strategy_data.iterrows():
        rank = row['AHP排名']
        # 线性插值：排名1对应4%，排名123对应15%
        base_rate = MIN_RATE + (rank - 1) * RATE_RANGE / (total_companies - 1)
        base_rates.append(base_rate)
    
    # 风险等级调整
    risk_adjustments = {
        '低风险': -0.005,     # 优惠0.5个百分点
        '中等风险': 0.0,      # 无调整
        '高风险': 0.01,       # 上浮1个百分点
        '极高风险': 0.02      # 上浮2个百分点
    }
    
    final_rates = []
    for i, (idx, row) in enumerate(strategy_data.iterrows()):
        base_rate = base_rates[i]
        risk_level = row['风险等级']
        adjustment = risk_adjustments.get(risk_level, 0)
        
        final_rate = base_rate + adjustment
        
        # 确保利率在4%-15%范围内
        final_rate = max(MIN_RATE, min(MAX_RATE, final_rate))
        final_rates.append(final_rate)
    
    return base_rates, final_rates

# 计算利率
base_rates, final_rates = calculate_interest_rates(ahp_strategy)

# 添加利率信息到数据框
ahp_strategy['基础利率(%)'] = [rate * 100 for rate in base_rates]
ahp_strategy['最终利率(%)'] = [rate * 100 for rate in final_rates]

print("利率分配完成！")

print(f"\n3. 利率分配结果统计")
print("="*50)

print(f"利率统计信息:")
print(f"最低利率: {min(final_rates)*100:.2f}%")
print(f"最高利率: {max(final_rates)*100:.2f}%")
print(f"平均利率: {np.mean(final_rates)*100:.2f}%")
print(f"利率标准差: {np.std(final_rates)*100:.2f}%")

# 按风险等级统计利率
print(f"\n各风险等级利率统计:")
risk_rate_stats = ahp_strategy.groupby('风险等级')['最终利率(%)'].agg(['count', 'mean', 'min', 'max']).round(2)
risk_rate_stats.columns = ['企业数量', '平均利率(%)', '最低利率(%)', '最高利率(%)']
print(risk_rate_stats)

print(f"\n4. 前30名企业利率方案")
print("="*80)

# 按AHP排名排序显示前30名
top_30 = ahp_strategy.sort_values('AHP排名').head(30)

print("排名  企业代号  AHP得分   分配比例(%)  风险等级   基础利率(%)  最终利率(%)")
print("-" * 80)

for idx, row in top_30.iterrows():
    print(f"{row['AHP排名']:>3}   {row['企业代号']:>4}    {row['AHP综合得分']:>6.4f}    {row['最终分配比例(%)']:>7.4f}   {row['风险等级']:>6}   {row['基础利率(%)']:>7.2f}     {row['最终利率(%)']:>7.2f}")

print(f"\n5. 利率梯度分析")
print("="*50)

# 创建利率区间统计
rate_bins = [4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]
rate_labels = ['4-5%', '5-6%', '6-7%', '7-8%', '8-9%', '9-10%', '10-11%', '11-12%', '12-13%', '13-14%', '14-15%']

ahp_strategy['利率区间'] = pd.cut(ahp_strategy['最终利率(%)'], bins=rate_bins, labels=rate_labels, include_lowest=True)

rate_distribution = ahp_strategy.groupby('利率区间').agg({
    '企业代号': 'count',
    '最终分配比例(%)': 'sum'
}).round(2)
rate_distribution.columns = ['企业数量', '分配比例总和(%)']

print("利率区间分布:")
print(rate_distribution)

print(f"\n6. 优质客户利率优惠分析")
print("="*50)

# 分析前20%企业的利率情况
top_20_percent = int(len(ahp_strategy) * 0.2)
premium_clients = ahp_strategy.sort_values('AHP排名').head(top_20_percent)

print(f"前20%优质客户({top_20_percent}家)利率分析:")
print(f"平均利率: {premium_clients['最终利率(%)'].mean():.2f}%")
print(f"利率范围: {premium_clients['最终利率(%)'].min():.2f}% - {premium_clients['最终利率(%)'].max():.2f}%")
print(f"分配比例总和: {premium_clients['最终分配比例(%)'].sum():.2f}%")

# 分析高风险客户利率
high_risk_clients = ahp_strategy[ahp_strategy['风险等级'].isin(['高风险', '极高风险'])]
print(f"\n高风险客户({len(high_risk_clients)}家)利率分析:")
print(f"平均利率: {high_risk_clients['最终利率(%)'].mean():.2f}%")
print(f"利率范围: {high_risk_clients['最终利率(%)'].min():.2f}% - {high_risk_clients['最终利率(%)'].max():.2f}%")
print(f"分配比例总和: {high_risk_clients['最终分配比例(%)'].sum():.2f}%")

print(f"\n7. 利率收益分析")
print("="*50)

# 计算加权平均利率（基于分配比例权重）
weighted_avg_rate = (ahp_strategy['最终利率(%)'] * ahp_strategy['最终分配比例(%)']).sum() / 100

print(f"加权平均利率: {weighted_avg_rate:.2f}%")

# 各风险等级收益权重分析
risk_revenue_weight = ahp_strategy.groupby('风险等级').agg({
    '最终分配比例(%)': 'sum',
    '最终利率(%)': 'mean'
}).round(2)
risk_revenue_weight.columns = ['分配权重(%)', '平均利率(%)']
risk_revenue_weight = risk_revenue_weight.sort_values('分配权重(%)', ascending=False)

print(f"\n各风险等级分配权重和利率:")
for risk_level, row in risk_revenue_weight.iterrows():
    print(f"{risk_level:>6}: 分配权重{row['分配权重(%)']:>6.2f}%, 平均利率{row['平均利率(%)']:>6.2f}%")

print(f"\n8. 保存利率分配结果")
print("="*50)

# 按AHP排名重新排序
final_result = ahp_strategy.sort_values('AHP排名')[['企业代号', 'AHP综合得分', 'AHP排名', '最终分配比例(%)', 
                                                  '风险等级', '基础利率(%)', '最终利率(%)']]

# 保存详细结果
final_result.round(4).to_csv('AHP差异化利率分配策略.csv', index=False, encoding='utf-8-sig')
print("✅ 差异化利率策略已保存至: AHP差异化利率分配策略.csv")

# 创建简化版给银行使用
bank_summary = final_result[['企业代号', 'AHP排名', '最终分配比例(%)', '最终利率(%)', '风险等级']].copy()
bank_summary.columns = ['企业代号', '信用排名', '信贷分配比例(%)', '执行年利率(%)', '风险等级']
bank_summary.to_csv('银行执行版_企业利率表.csv', index=False, encoding='utf-8-sig')
print("✅ 银行执行版利率表已保存至: 银行执行版_企业利率表.csv")

print(f"\n9. 利率策略执行建议")
print("="*50)
print(f"""
🎯 差异化利率策略执行方案:

📊 利率结构特点:
• 利率区间: 4.00% - 15.00%
• 加权平均利率: {weighted_avg_rate:.2f}%
• 风险差异化明显: 低风险vs高风险利率差达{high_risk_clients['最终利率(%)'].mean() - premium_clients['最终利率(%)'].mean():.1f}个百分点

💰 分配权重结构:
• 低风险客户: {risk_revenue_weight.loc['低风险', '分配权重(%)']:.1f}% (平均利率{risk_revenue_weight.loc['低风险', '平均利率(%)']:.2f}%)
• 中等风险客户: {risk_revenue_weight.loc['中等风险', '分配权重(%)']:.1f}% (平均利率{risk_revenue_weight.loc['中等风险', '平均利率(%)']:.2f}%)
• 优质客户(前20%)平均利率: {premium_clients['最终利率(%)'].mean():.2f}%

🔍 风险等级说明:
• 极高风险: AHP得分最低10%企业 ({risk_rate_stats.loc['极高风险', '企业数量']}家)
• 高风险: AHP得分10%-30%企业 ({risk_rate_stats.loc['高风险', '企业数量']}家)  
• 中等风险: AHP得分30%-70%企业 ({risk_rate_stats.loc['中等风险', '企业数量']}家)
• 低风险: AHP得分最高30%企业 ({risk_rate_stats.loc['低风险', '企业数量']}家)

✅ 执行优势:
1. 科学定价: 基于AHP综合评估，确保风险与收益匹配
2. 客户分层: 4个风险等级，差异化服务
3. 百分比分配: 灵活适应不同信贷总额规模
4. 竞争优势: 优质客户享受优惠利率，增强客户粘性

📋 实施建议:
• 对前30名企业给予优质客户待遇
• 定期评估客户风险等级，动态调整利率
• 建立预警机制，重点关注高风险客户
• 结合市场利率变化，适时调整利率政策
""")

print("="*80)
print("AHP差异化利率分配策略制定完成！")
print("="*80)
