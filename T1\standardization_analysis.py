"""
分析标准化到0-1区间对各评估方法的影响

本文件分析将四项指标标准化到0-1区间是否会与各方法原理冲突
"""

print("="*60)
print("标准化到0-1区间对各评估方法的影响分析")
print("="*60)

print("""
1. 层次分析法 (AHP)
==================
原理：加权线性组合
影响：✅ 无冲突
- AHP本质是线性加权，标准化不改变相对关系
- 标准化使各指标权重更加准确
- 建议：标准化到0-1区间完全适用

2. 主成分分析法 (PCA) 
=====================
原理：寻找数据主要变异方向
影响：⚠️ 需要注意
- PCA通常使用Z-score标准化（均值0，方差1）
- Min-Max标准化（0-1区间）也可以使用，但可能改变方差结构
- 当前使用：适用，但Z-score标准化可能更好

3. TOPSIS法
===========
原理：基于理想解和负理想解的距离
影响：✅ 无冲突  
- TOPSIS内部就包含标准化步骤
- 0-1标准化使距离计算更合理
- 建议：完全适用

4. 熵权法
=========
原理：基于数据分散程度确定权重
影响：✅ 无冲突
- 熵权法对数据分布敏感，标准化有助于公平比较
- 0-1标准化不改变各指标内部的相对分散程度
- 建议：完全适用
""")

print("\n" + "="*50)
print("结论与建议")
print("="*50)

print("""
总体结论：✅ 标准化到0-1区间对所有方法都是有益的

具体分析：
1. 消除量纲影响：不同指标（分数、金额、比率）统一到同一尺度
2. 权重更准确：避免大数值指标主导结果
3. 计算稳定：避免数值溢出和精度问题
4. 结果可比：所有企业在同一评价标准下比较

潜在改进：
- PCA可考虑使用StandardScaler（Z-score标准化）获得更好效果
- 当前Min-Max标准化已经足够好，结果合理

最终建议：
✅ 继续使用0-1标准化，方法科学合理
✅ 当前实现已经很好，无需大改动
✅ 如需优化，可为PCA单独使用Z-score标准化
""")

print("\n" + "="*50)
print("标准化方法对比")
print("="*50)

import pandas as pd
import numpy as np
from sklearn.preprocessing import MinMaxScaler, StandardScaler

# 读取原始数据进行演示
data = pd.read_csv('企业四项指标原始数据.csv')
indicators = ['最终信用分数', '盈利能力', '企业规模', '稳定性']

print("原始数据统计:")
print(data[indicators].describe())

# Min-Max标准化 (0-1)
scaler_minmax = MinMaxScaler()
data_minmax = pd.DataFrame(
    scaler_minmax.fit_transform(data[indicators]),
    columns=[f'{col}_MinMax' for col in indicators]
)

print("\nMin-Max标准化后 (0-1区间):")
print(data_minmax.describe())

# Z-score标准化
scaler_std = StandardScaler()
data_zscore = pd.DataFrame(
    scaler_std.fit_transform(data[indicators]),
    columns=[f'{col}_ZScore' for col in indicators]
)

print("\nZ-score标准化后 (均值0,方差1):")
print(data_zscore.describe())

print(f"\n" + "="*50)
print("相关性分析")
print("="*50)

# 计算不同标准化方法之间的相关性
correlation_minmax_zscore = []
for i, col in enumerate(indicators):
    corr = np.corrcoef(data_minmax.iloc[:, i], data_zscore.iloc[:, i])[0, 1]
    correlation_minmax_zscore.append(corr)
    print(f"{col}: Min-Max vs Z-score 相关性 = {corr:.6f}")

print(f"\n平均相关性: {np.mean(correlation_minmax_zscore):.6f}")

if np.mean(correlation_minmax_zscore) > 0.99:
    print("✅ 两种标准化方法结果高度相关，Min-Max标准化完全可行")
else:
    print("⚠️  两种标准化方法存在差异，需要根据具体方法选择")
