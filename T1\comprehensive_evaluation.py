import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.decomposition import PCA
import matplotlib.pyplot as plt
import seaborn as sns

# 读取各个指标数据
print("正在读取各项指标数据...")

# 1. 企业信用分数
credit_df = pd.read_csv('企业信用评分结果.csv')
credit_scores = credit_df[['企业代号', '最终信用分数']].copy()

# 2. 月度差值中位数（盈利能力）
profit_df = pd.read_csv('企业月度差值中位数结果.csv')
profit_ability = profit_df[['企业代号', '月度差值中位数']].copy()
profit_ability.rename(columns={'月度差值中位数': '盈利能力'}, inplace=True)

# 3. 月度绝对值之和平均值（企业规模）
scale_df = pd.read_csv('企业月度绝对值之和平均值结果.csv')
company_scale = scale_df[['企业代号', '月度绝对值之和平均值', '月度绝对值之和标准差']].copy()
company_scale.rename(columns={
    '月度绝对值之和平均值': '企业规模',
    '月度绝对值之和标准差': '稳定性指标'
}, inplace=True)

# 合并所有指标
print("正在合并所有指标...")
final_data = credit_scores.merge(profit_ability, on='企业代号', how='left')
final_data = final_data.merge(company_scale, on='企业代号', how='left')

# 处理缺失值
final_data = final_data.fillna(0)

# 稳定性指标转换（标准差越小越稳定，所以要反向处理）
# 使用1/(1+std)的形式，值越大表示越稳定
final_data['稳定性'] = 1 / (1 + final_data['稳定性指标'] / final_data['稳定性指标'].max())

print("合并后的数据示例:")
print(final_data.head())
print(f"\n数据形状: {final_data.shape}")

# 保存原始指标数据
final_data.to_csv('企业四项指标原始数据.csv', index=False, encoding='utf-8-sig')

print("\n=== 四项指标的基本统计信息 ===")
indicators = ['最终信用分数', '盈利能力', '企业规模', '稳定性']
stats = final_data[indicators].describe()
print(stats)

# ===== 方法1: 层次分析法 (AHP) =====
print("\n" + "="*50)
print("方法1: 层次分析法 (AHP) 权重分配")
print("="*50)

# 设定权重（可根据实际需要调整）
weights_ahp = {
    '最终信用分数': 0.4,  # 信用风险最重要
    '盈利能力': 0.3,      # 盈利能力次之
    '企业规模': 0.2,      # 企业规模
    '稳定性': 0.1         # 稳定性
}

print("AHP权重设定:")
for indicator, weight in weights_ahp.items():
    print(f"  {indicator}: {weight}")

# 数据标准化（0-100分制）
scaler_minmax = MinMaxScaler(feature_range=(0, 100))
data_normalized = final_data.copy()

# 对每个指标进行标准化
for indicator in indicators:
    if indicator == '盈利能力':
        # 盈利能力可能有负值，需要特殊处理
        min_val = final_data[indicator].min()
        max_val = final_data[indicator].max()
        data_normalized[f'{indicator}_标准化'] = ((final_data[indicator] - min_val) / (max_val - min_val)) * 100
    else:
        data_normalized[f'{indicator}_标准化'] = scaler_minmax.fit_transform(final_data[[indicator]]).flatten() * 100

print("\n标准化后的指标统计信息:")
standardized_indicators = ['最终信用分数_标准化', '盈利能力_标准化', '企业规模_标准化', '稳定性_标准化']
print(data_normalized[standardized_indicators].describe())

# 计算AHP综合得分
data_normalized['AHP综合得分'] = (
    data_normalized['最终信用分数_标准化'] * weights_ahp['最终信用分数'] +
    data_normalized['盈利能力_标准化'] * weights_ahp['盈利能力'] +
    data_normalized['企业规模_标准化'] * weights_ahp['企业规模'] +
    data_normalized['稳定性_标准化'] * weights_ahp['稳定性']
) * 100

# ===== 方法2: 主成分分析 (PCA) =====
print("\n" + "="*50)
print("方法2: 主成分分析 (PCA)")
print("="*50)

# 使用标准化后的数据进行PCA
pca_data = data_normalized[standardized_indicators].values

# 执行PCA
pca = PCA(n_components=4)
pca_result = pca.fit_transform(pca_data)

print("PCA解释方差比:")
for i, ratio in enumerate(pca.explained_variance_ratio_):
    print(f"  PC{i+1}: {ratio:.3f} ({ratio*100:.1f}%)")

# 计算PCA综合得分（使用前两个主成分）
data_normalized['PCA综合得分'] = (
    pca_result[:, 0] * pca.explained_variance_ratio_[0] +
    pca_result[:, 1] * pca.explained_variance_ratio_[1]
)

# 转换为0-100分制
min_pca = data_normalized['PCA综合得分'].min()
max_pca = data_normalized['PCA综合得分'].max()
data_normalized['PCA综合得分'] = ((data_normalized['PCA综合得分'] - min_pca) / (max_pca - min_pca)) * 100

# ===== 方法3: TOPSIS法 =====
print("\n" + "="*50)
print("方法3: TOPSIS法（逼近理想解排序法）")
print("="*50)

# TOPSIS权重
weights_topsis = [0.4, 0.3, 0.2, 0.1]  # 对应四个指标

def topsis_method(data, weights, standardized_indicators):
    # 使用已标准化的数据
    normalized_matrix = data[standardized_indicators].values
    
    # 进一步归一化（向量归一化）
    for j in range(len(standardized_indicators)):
        norm = np.sqrt(np.sum(normalized_matrix[:, j] ** 2))
        if norm > 0:
            normalized_matrix[:, j] = normalized_matrix[:, j] / norm
    
    # 加权标准化矩阵
    weighted_matrix = normalized_matrix * weights
    
    # 确定正理想解和负理想解
    ideal_best = np.max(weighted_matrix, axis=0)
    ideal_worst = np.min(weighted_matrix, axis=0)
    
    # 计算距离
    dist_best = np.sqrt(np.sum((weighted_matrix - ideal_best) ** 2, axis=1))
    dist_worst = np.sqrt(np.sum((weighted_matrix - ideal_worst) ** 2, axis=1))
    
    # 计算相对贴近度
    scores = dist_worst / (dist_best + dist_worst)
    
    return scores * 100  # 转为0-100分制

data_normalized['TOPSIS综合得分'] = topsis_method(data_normalized, weights_topsis, standardized_indicators)

# ===== 方法4: 熵权法 =====
print("\n" + "="*50)
print("方法4: 熵权法（客观赋权）")
print("="*50)

def entropy_weight(data):
    # 数据标准化
    data_norm = data.div(data.sum(axis=0), axis=1)
    
    # 计算熵值
    entropy = -np.sum(data_norm * np.log(data_norm + 1e-10), axis=0) / np.log(len(data))
    
    # 计算权重
    weights = (1 - entropy) / np.sum(1 - entropy)
    
    return weights

# 为熵权法准备标准化后的数据
entropy_data = data_normalized[standardized_indicators].copy()
# 确保所有数据为正值（加1避免零值）
entropy_data = entropy_data + 1

entropy_weights = entropy_weight(entropy_data)
print("熵权法计算的权重:")
for i, indicator in enumerate(standardized_indicators):
    print(f"  {indicator}: {entropy_weights.iloc[i]:.3f}")

# 计算熵权法综合得分
normalized_entropy_data = entropy_data / entropy_data.max()
print(f"After normalization shape: {normalized_entropy_data.shape}")

data_normalized['熵权法综合得分'] = np.dot(normalized_entropy_data.values, entropy_weights.values) * 100

# ===== 综合评估结果 =====
print("\n" + "="*50)
print("综合评估结果对比")
print("="*50)

# 选择关键列进行展示（使用标准化后的数据）
result_columns = ['企业代号', '最终信用分数_标准化', '盈利能力_标准化', '企业规模_标准化', '稳定性_标准化',
                 'AHP综合得分', 'PCA综合得分', 'TOPSIS综合得分', '熵权法综合得分']

final_result = data_normalized[result_columns].copy()

# 重命名标准化列为更清晰的名称
final_result.rename(columns={
    '最终信用分数_标准化': '信用分数(标准化)',
    '盈利能力_标准化': '盈利能力(标准化)', 
    '企业规模_标准化': '企业规模(标准化)',
    '稳定性_标准化': '稳定性(标准化)'
}, inplace=True)

# 同时保留原始数据用于参考
final_result['信用分数(原始)'] = data_normalized['最终信用分数']
final_result['盈利能力(原始)'] = data_normalized['盈利能力'] 
final_result['企业规模(原始)'] = data_normalized['企业规模']
final_result['稳定性(原始)'] = data_normalized['稳定性']

# 计算平均综合得分
score_columns = ['AHP综合得分', 'PCA综合得分', 'TOPSIS综合得分', '熵权法综合得分']
final_result['平均综合得分'] = final_result[score_columns].mean(axis=1)

# 排序
final_result = final_result.sort_values('平均综合得分', ascending=False)

print("前10名企业（显示标准化后的指标）:")
display_columns = ['企业代号', '信用分数(标准化)', '盈利能力(标准化)', '企业规模(标准化)', '稳定性(标准化)',
                  'AHP综合得分', 'PCA综合得分', 'TOPSIS综合得分', '熵权法综合得分', '平均综合得分']
print(final_result[display_columns].head(10))

print("\n后10名企业（显示标准化后的指标）:")
print(final_result[display_columns].tail(10))

# 保存最终结果（包含标准化和原始数据）
final_result.to_csv('企业综合评估结果_完整版.csv', index=False, encoding='utf-8-sig')

# 同时保存简化版（只包含标准化数据和评分）
simplified_result = final_result[display_columns].copy()
simplified_result.to_csv('企业综合评估结果_标准化.csv', index=False, encoding='utf-8-sig')

print(f"\n完整评估结果已保存到: 企业综合评估结果_完整版.csv")
print(f"标准化评估结果已保存到: 企业综合评估结果_标准化.csv")

# ===== 方法推荐 =====
print("\n" + "="*50)
print("方法推荐与建议")
print("="*50)

print("""
根据您的四个指标，推荐以下综合评估方法：

1. 【推荐】层次分析法 (AHP)
   - 适用场景: 当您能明确各指标重要性时
   - 优点: 结果直观，易于解释和调整
   - 建议权重: 信用分数(40%) > 盈利能力(30%) > 企业规模(20%) > 稳定性(10%)

2. TOPSIS法
   - 适用场景: 需要考虑理想解和最差解的相对位置
   - 优点: 充分利用原始数据信息，结果稳定

3. 熵权法
   - 适用场景: 希望客观赋权，减少主观因素
   - 优点: 权重完全由数据决定

4. 平均综合得分
   - 适用场景: 综合多种方法的优点
   - 优点: 避免单一方法的局限性

建议采用层次分析法作为主要方法，同时参考其他方法的结果进行验证。
""")

# 相关性分析
print("\n各方法结果相关性分析:")
correlation = final_result[score_columns + ['平均综合得分']].corr()
print(correlation)
