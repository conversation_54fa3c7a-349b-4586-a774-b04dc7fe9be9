"""
步骤1：计算T2企业的7项财务指标（除负债水平外）
基于有效发票数据，计算302家无信贷记录企业的财务指标
"""
import pandas as pd
import numpy as np

print("="*80)
print("步骤1：计算T2企业的7项财务指标")
print("="*80)

# 读取T2企业基础数据
enterprises = pd.read_csv('T2.1.csv')
print(f"T2企业总数: {len(enterprises)}家")

# 读取发票数据
print("正在读取发票数据...")
input_data = pd.read_csv('T2.3.csv')  # 进项发票
output_data = pd.read_csv('T2.2.csv')  # 销项发票

print(f"进项发票总数: {len(input_data)}条")
print(f"销项发票总数: {len(output_data)}条")

# 筛选有效发票
input_data_valid = input_data[input_data['发票状态'] == '有效发票'].copy()
output_data_valid = output_data[output_data['发票状态'] == '有效发票'].copy()

print(f"有效进项发票: {len(input_data_valid)}条")
print(f"有效销项发票: {len(output_data_valid)}条")

# 获取企业代号列表
company_list = enterprises['企业代号'].tolist()
print(f"待计算企业: {len(company_list)}家 (E124-E{123+len(company_list)})")

def calculate_seven_indicators(company_list, input_data, output_data):
    """
    计算7项财务指标（除负债水平外）：
    1. 盈利能力：月度差值中位数（销项-进项）
    2. 现金流稳定性：基于月度差值标准差的稳定性指标
    3. 企业规模：月度绝对值之和的平均值
    4. 税负压力：销项税率 - 进项税率
    5. 公司市场竞争力：销项总额 / (进项总额 + 1)
    6. 盈利预测可靠性：销项发票数 / 总发票数
    7. 经营风险：税率差异 + 发票不平衡系数
    """
    results = {}
    
    print("  正在逐个企业计算财务指标...")
    
    for i, company in enumerate(company_list):
        if (i + 1) % 50 == 0:  # 每50家企业显示进度
            print(f"    已处理: {i+1}/{len(company_list)} 家企业")
            
        # 获取该企业的有效发票数据
        company_input = input_data[input_data['企业代号'] == company].copy()
        company_output = output_data[output_data['企业代号'] == company].copy()
        
        if len(company_input) == 0 and len(company_output) == 0:
            # 无发票数据的企业设为默认值
            results[company] = {
                '盈利能力': 0,
                '现金流稳定性': 0.5,
                '企业规模': 0,
                '税负压力': 0,
                '公司市场竞争力': 1.0,
                '盈利预测可靠性': 0.5,
                '经营风险': 0.5
            }
            continue
            
        # 按月汇总数据
        # 处理进项数据
        if len(company_input) > 0:
            company_input['开票日期'] = pd.to_datetime(company_input['开票日期'], format='%m/%d/%Y')
            company_input['年月'] = company_input['开票日期'].dt.to_period('M')
            monthly_input = company_input.groupby('年月').agg({
                '金额': 'sum',
                '税额': 'sum',
                '价税合计': 'sum'
            }).reset_index()
            monthly_input.rename(columns={
                '金额': '进项金额',
                '税额': '进项税额', 
                '价税合计': '进项价税合计'
            }, inplace=True)
        else:
            monthly_input = pd.DataFrame(columns=['年月', '进项金额', '进项税额', '进项价税合计'])
            
        # 处理销项数据
        if len(company_output) > 0:
            company_output['开票日期'] = pd.to_datetime(company_output['开票日期'], format='%Y/%m/%d')
            company_output['年月'] = company_output['开票日期'].dt.to_period('M')
            monthly_output = company_output.groupby('年月').agg({
                '金额': 'sum',
                '税额': 'sum',
                '价税合计': 'sum'
            }).reset_index()
            monthly_output.rename(columns={
                '金额': '销项金额',
                '税额': '销项税额',
                '价税合计': '销项价税合计'
            }, inplace=True)
        else:
            monthly_output = pd.DataFrame(columns=['年月', '销项金额', '销项税额', '销项价税合计'])
            
        # 合并月度数据
        if len(monthly_input) > 0 and len(monthly_output) > 0:
            monthly_combined = pd.merge(monthly_input, monthly_output, on='年月', how='outer')
        elif len(monthly_input) > 0:
            monthly_combined = monthly_input.copy()
            monthly_combined[['销项金额', '销项税额', '销项价税合计']] = 0
        elif len(monthly_output) > 0:
            monthly_combined = monthly_output.copy()
            monthly_combined[['进项金额', '进项税额', '进项价税合计']] = 0
        else:
            monthly_combined = pd.DataFrame()
            
        monthly_combined = monthly_combined.fillna(0)
        
        if len(monthly_combined) == 0:
            results[company] = {
                '盈利能力': 0,
                '现金流稳定性': 0.5,
                '企业规模': 0,
                '税负压力': 0,
                '公司市场竞争力': 1.0,
                '盈利预测可靠性': 0.5,
                '经营风险': 0.5
            }
            continue
            
        # 计算各项指标
        
        # 1. 盈利能力：月度差值中位数
        monthly_combined['月度差值'] = monthly_combined['销项价税合计'] - monthly_combined['进项价税合计']
        profit_ability = monthly_combined['月度差值'].median()
        
        # 2. 现金流稳定性：1 / (1 + 变异系数)
        if monthly_combined['月度差值'].std() > 0 and monthly_combined['月度差值'].mean() != 0:
            cv = abs(monthly_combined['月度差值'].std() / monthly_combined['月度差值'].mean())
            cash_flow_stability = 1 / (1 + cv)
        else:
            cash_flow_stability = 0.5
        
        # 3. 企业规模：月度绝对值之和的平均值
        monthly_combined['月度绝对值之和'] = (monthly_combined['进项价税合计'].abs() + 
                                     monthly_combined['销项价税合计'].abs())
        enterprise_scale = monthly_combined['月度绝对值之和'].mean()
        
        # 计算税率
        total_input_amount = monthly_combined['进项金额'].sum()
        total_input_tax = monthly_combined['进项税额'].sum()
        total_output_amount = monthly_combined['销项金额'].sum()
        total_output_tax = monthly_combined['销项税额'].sum()
        
        input_tax_rate = total_input_tax / total_input_amount if total_input_amount > 0 else 0
        output_tax_rate = total_output_tax / total_output_amount if total_output_amount > 0 else 0
        
        # 4. 税负压力：销项税率 - 进项税率
        tax_pressure = output_tax_rate - input_tax_rate
        
        # 5. 公司市场竞争力：销项总额 / (进项总额 + 1)
        total_input_total = monthly_combined['进项价税合计'].sum()
        total_output_total = monthly_combined['销项价税合计'].sum()
        market_competitiveness = total_output_total / (total_input_total + 1)
        
        # 6. 盈利预测可靠性：销项发票数 / 总发票数
        input_invoice_count = len(company_input)
        output_invoice_count = len(company_output)
        total_invoice_count = input_invoice_count + output_invoice_count
        if total_invoice_count > 0:
            profit_predictability = output_invoice_count / total_invoice_count
        else:
            profit_predictability = 0.5
        
        # 7. 经营风险：税率差异绝对值 + 发票不平衡系数
        tax_rate_diff = abs(input_tax_rate - output_tax_rate)
        if total_invoice_count > 0:
            invoice_imbalance = abs(input_invoice_count - output_invoice_count) / total_invoice_count
        else:
            invoice_imbalance = 0
        business_risk = tax_rate_diff + invoice_imbalance
        
        results[company] = {
            '盈利能力': profit_ability,
            '现金流稳定性': cash_flow_stability,
            '企业规模': enterprise_scale,
            '税负压力': tax_pressure,
            '公司市场竞争力': market_competitiveness,
            '盈利预测可靠性': profit_predictability,
            '经营风险': business_risk
        }
    
    print(f"    财务指标计算完成: {len(company_list)}家企业")
    return results

# 计算7项财务指标
indicators_results = calculate_seven_indicators(company_list, input_data_valid, output_data_valid)

# 转换为DataFrame
indicators_df = pd.DataFrame.from_dict(indicators_results, orient='index').reset_index()
indicators_df.rename(columns={'index': '企业代号'}, inplace=True)

print(f"\n✅ T2企业7项财务指标统计摘要:")
indicators = ['盈利能力', '现金流稳定性', '企业规模', '税负压力', 
              '公司市场竞争力', '盈利预测可靠性', '经营风险']

for indicator in indicators:
    data = indicators_df[indicator]
    print(f"{indicator:12s}: min={data.min():10.4f}, max={data.max():10.4f}, mean={data.mean():8.4f}")

# 保存T2企业的7项财务指标
indicators_df.to_csv('T2企业_七项财务指标.csv', index=False, encoding='utf-8-sig')
print(f"\n✅ T2企业7项财务指标已保存至: T2企业_七项财务指标.csv")

print(f"\n前10家T2企业财务指标预览:")
print("企业代号 |   盈利能力 | 现金流稳定性 |   企业规模 | 税负压力 | 市场竞争力 | 盈利可靠性 | 经营风险")
print("-" * 95)
for _, row in indicators_df.head(10).iterrows():
    print(f"{row['企业代号']:>6} | {row['盈利能力']:>10.0f} | {row['现金流稳定性']:>10.4f} | {row['企业规模']:>10.0f} | "
          f"{row['税负压力']:>7.4f} | {row['公司市场竞争力']:>8.4f} | {row['盈利预测可靠性']:>8.4f} | {row['经营风险']:>6.4f}")

print(f"\n下一步：与T1的99家合格企业进行相似度匹配")
